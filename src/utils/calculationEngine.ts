import { InnerTextJobSpecs, InnerTextProdParams } from '@/stores/innerTextStore';
import { CoverJobSpecs, CoverProdParams } from '@/stores/coverStore';
import { EndpapersJobSpecs, EndpapersProdParams } from '@/stores/endpapersStore';

// Placeholder for PaperOption data structure
export interface PaperOptionData {
  id: string | number;
  paperName: string;
  source: 'Pre-Cut' | 'Roll';
  sheetH_input: number; // Original input height
  sheetW_input: number; // Original input width
  grainDirInput: 'Height' | 'Width'; // Original grain direction
  caliperMicrons: number;
  costReam?: number; // For Pre-Cut
  gsm?: number; // For Roll (and Pre-Cut if provided)
  costTonne?: number; // For Roll
  // ... any other raw paper properties
  error?: boolean;
  errorMessageKey?: string;
}

// Placeholder for the detailed result of a single paper option calculation
export interface CalculationResult extends PaperOptionData {
  // Press & Usable Area
  pressH?: number;
  pressW?: number;
  paperWasRotated?: boolean;
  pressSizeNoteKey?: string; // e.g., 'resNoteRotated', 'resNoteTrimmed'
  originalH?: number; // If trimmed from a larger sheet
  originalW?: number; // If trimmed from a larger sheet
  usableH?: number;
  usableW?: number;
  isLongGrainParallelToPressH?: boolean; // Grain relative to press sheet

  // Layout & Fit
  layoutPageH?: number; // Item height with bleed
  layoutPageW?: number; // Item width with bleed
  maxItemsPerSide?: number; // Pages for text, covers for cover, leaves for endpaper
  layoutOrientation?: 'A' | 'B' | ''; // A: ItemH || UsableH, B: ItemW || UsableH
  winningLayoutDownPages?: number; // How many items fit down
  winningLayoutAcrossPages?: number; // How many items fit across
  occupiedHeight?: number; // Actual height used by imposed items
  occupiedWidth?: number; // Actual width used by imposed items
  layoutDescKey?: string; // e.g., 'layoutDescSpreadAcross'
  grainAlignmentStatus?: 'Aligned' | 'Misaligned' | 'N/A' | string;

  // Output & Cost
  pagesPerSheetOutput?: number; // e.g., 32p for a 16-up signature, or N-up for covers/endpapers
  totalSheetsNeeded?: number;
  costPerSheet?: number;
  totalCost?: number;
  wastePercent?: number;
  utilisationRate?: number; // 1 - wastePercent

  // Component-specific metrics
  bookBlockThickness_mm?: number; // For Inner Text
  costPerBook?: number; // For Inner Text (cost per final book's inner text)
  costPerCover?: number; // For Cover
  costPerEndpaperSet?: number; // For Endpapers

  // Meta
  isOptimalCandidate?: boolean; // Compared to benchmark
  inputNoteKey?: string; // e.g. 'resNoteHeightInferred'
  rollDimensionOptimizedForFitting?: boolean;
  rollOptimizationNoteKey?: string;
}

// Extended CalculationResult or a similar structure will be the type for candidateA/B
type LayoutCandidate = Partial<CalculationResult> & {
  maxItemsPerSide: number;
  grainAlignmentStatus: 'Aligned' | 'Misaligned' | string;
  occupiedHeight: number;
  occupiedWidth: number;
  usableH?: number;
  usableW?: number;
  rollDimensionOptimizedForFitting?: boolean;
};


// --- Main Orchestrator ---
export const calculateAllPaperOptions = (
  jobInputs: InnerTextJobSpecs | CoverJobSpecs | EndpapersJobSpecs,
  prodParams: InnerTextProdParams | CoverProdParams | EndpapersProdParams,
  paperOptions: PaperOptionData[],
  componentType: 'innerText' | 'cover' | 'endpapers'
): CalculationResult[] => {
  console.log('Starting calculations for:', componentType, jobInputs, prodParams, paperOptions);
  const results: CalculationResult[] = [];

  let trimHeightMm: number, trimWidthMm: number;
  let bindingMethod: string | undefined; // Inner Text
  let coverType: string | undefined; // Cover
  let spineThicknessMm: number | undefined; // Cover
  let flapWidthMm: number | undefined; // Cover

  let bleedMm: number, gripperMm: number, colorBarMm: number;
  let lipMm: number | undefined; // Inner Text
  let alignmentMode: 'Aligned' | 'Misaligned';


  if (componentType === 'innerText') {
    const innerTextJobInputs = jobInputs as InnerTextJobSpecs;
    const innerTextProdParams = prodParams as InnerTextProdParams;
    trimHeightMm = safeParseFloat(innerTextJobInputs.trimHeightMm);
    trimWidthMm = safeParseFloat(innerTextJobInputs.trimWidthMm);
    // Read totalPages but don't store it in a variable
    safeParseFloat(innerTextJobInputs.totalPages);
    bindingMethod = innerTextJobInputs.bindingMethod;
    bleedMm = safeParseFloat(innerTextProdParams.bleedMm);
    gripperMm = safeParseFloat(innerTextProdParams.gripperMm);
    colorBarMm = safeParseFloat(innerTextProdParams.colorBarMm);
    lipMm = safeParseFloat(innerTextProdParams.lipMm);
    alignmentMode = innerTextProdParams.alignmentMode;
  } else if (componentType === 'cover') {
    const coverJobInputs = jobInputs as CoverJobSpecs;
    const coverProdParams = prodParams as CoverProdParams;
    trimHeightMm = safeParseFloat(coverJobInputs.trimHeightMm);
    trimWidthMm = safeParseFloat(coverJobInputs.trimWidthMm);
    coverType = coverJobInputs.coverType;
    spineThicknessMm = safeParseFloat(coverJobInputs.spineThicknessMm);
    flapWidthMm = safeParseFloat(coverJobInputs.flapWidthMm);
    // Read turnInAllowanceMm but don't store it in a variable
    safeParseFloat(coverJobInputs.turnInAllowanceMm);
    bleedMm = safeParseFloat(coverProdParams.bleedMm);
    gripperMm = safeParseFloat(coverProdParams.gripperMm);
    colorBarMm = safeParseFloat(coverProdParams.colorBarMm);
    alignmentMode = coverProdParams.alignmentMode;
    lipMm = 0; // Covers typically don't have a side lip in imposition
  } else if (componentType === 'endpapers') {
    const endpapersJobInputs = jobInputs as EndpapersJobSpecs;
    const endpapersProdParams = prodParams as EndpapersProdParams;
    trimHeightMm = safeParseFloat(endpapersJobInputs.trimHeightMm);
    trimWidthMm = safeParseFloat(endpapersJobInputs.trimWidthMm);
    // Read endpaperType but don't store it in a variable
    endpapersJobInputs.endpaperType;
    bleedMm = safeParseFloat(endpapersProdParams.bleedMm);
    gripperMm = safeParseFloat(endpapersProdParams.gripperMm);
    colorBarMm = safeParseFloat(endpapersProdParams.colorBarMm);
    alignmentMode = endpapersProdParams.alignmentMode;
    lipMm = 0; // Endpapers typically don't have a side lip in imposition
  } else {
      console.error("Unknown component type:", componentType);
      return results; // Return empty array for unknown type
  }

  let benchmarkValue = 0;
  // Benchmark calculations for all types
  benchmarkValue = determineOptimalLayoutBenchmark_ComponentSpecific(
    componentType, MAX_PRESS_H, MAX_PRESS_W, gripperMm, colorBarMm, lipMm || 0, bleedMm, trimWidthMm, trimHeightMm, jobInputs
  );


  for (const rawPaperOption of paperOptions) {
    let currentResult: Partial<CalculationResult> = validatePaperOptionInputs(rawPaperOption);
    if (currentResult.error) {
      results.push(currentResult as CalculationResult);
      continue;
    }

    const initialFit = determineInitialPressFit(currentResult as PaperOptionData, MAX_PRESS_H, MAX_PRESS_W);
    currentResult = {
      ...currentResult,
      ...initialFit,
      originalH: currentResult.sheetH_input,
      originalW: currentResult.sheetW_input,
      rollDimensionOptimizedForFitting: initialFit.rollDimensionOptimizedForFitting
    };
    if (initialFit.error) {
      results.push(currentResult as CalculationResult);
      continue;
    }

    const pressFit = applyPressLimitsAndGetGrain(
      initialFit.sheetH_for_initial_fit,
      initialFit.sheetW_for_initial_fit,
      MAX_PRESS_H,
      MAX_PRESS_W,
      currentResult.grainDirInput!
    );
    currentResult = { ...currentResult, ...pressFit };

    const usableAreaResult = calculateUsableArea(pressFit.pressH, pressFit.pressW, gripperMm, colorBarMm, lipMm || 0); // Use 0 for lip if undefined
    currentResult = { ...currentResult, ...usableAreaResult };
    if (usableAreaResult.error) {
      results.push(currentResult as CalculationResult);
      continue;
    }

    let itemDimensions: { layoutPageH: number; layoutPageW: number } | null = null;

    // Calculate bleed dimensions based on component type
    if (componentType === 'innerText') {
      itemDimensions = calculateBleedDimensions_ComponentSpecific(componentType, trimWidthMm, trimHeightMm, bleedMm, bindingMethod);
    } else if (componentType === 'cover') {
         const coverJobInputs = jobInputs as CoverJobSpecs;
         itemDimensions = calculateBleedDimensions_ComponentSpecific(
            componentType, trimWidthMm, trimHeightMm, bleedMm, undefined, coverType, spineThicknessMm, flapWidthMm, safeParseFloat(coverJobInputs.turnInAllowanceMm) // Access from jobInputs
        );
    } else if (componentType === 'endpapers') {
         itemDimensions = calculateBleedDimensions_ComponentSpecific(
            componentType, trimWidthMm, trimHeightMm, bleedMm, undefined, undefined, undefined, undefined
        );
    }


    if (!itemDimensions || itemDimensions.layoutPageH <= 0 || itemDimensions.layoutPageW <= 0) {
      currentResult.error = true;
      currentResult.errorMessageKey = "errorPageDimsInvalid";
      results.push(currentResult as CalculationResult);
      continue;
    }
    currentResult.layoutPageH = itemDimensions.layoutPageH;
    currentResult.layoutPageW = itemDimensions.layoutPageW;

    // Calculate layout fit for both orientations
    const fitResultA = calculateSingleLayoutFit_ComponentSpecific(
      componentType, usableAreaResult.usableH, usableAreaResult.usableW, itemDimensions.layoutPageH, itemDimensions.layoutPageW, jobInputs
    );
    let candidateA: LayoutCandidate | null = null;
    if (fitResultA) {
      candidateA = {
        ...currentResult,
        maxItemsPerSide: fitResultA.fit,
        occupiedHeight: fitResultA.occupiedHeight,
        occupiedWidth: fitResultA.occupiedWidth,
        layoutOrientation: 'A',
        winningLayoutDownPages: fitResultA.layoutDown,
        winningLayoutAcrossPages: fitResultA.layoutAcross,
        grainAlignmentStatus: pressFit.isLongGrainParallelToPressH ? 'Aligned' : 'Misaligned',
      };
    }

    const fitResultB = calculateSingleLayoutFit_ComponentSpecific(
      componentType, usableAreaResult.usableH, usableAreaResult.usableW, itemDimensions.layoutPageW, itemDimensions.layoutPageH, jobInputs
    );
    let candidateB: LayoutCandidate | null = null;
    if (fitResultB) {
      candidateB = {
        ...currentResult,
        maxItemsPerSide: fitResultB.fit,
        occupiedHeight: fitResultB.occupiedHeight,
        occupiedWidth: fitResultB.occupiedWidth,
        layoutOrientation: 'B',
        winningLayoutDownPages: fitResultB.layoutDown,
        winningLayoutAcrossPages: fitResultB.layoutAcross,
        grainAlignmentStatus: !pressFit.isLongGrainParallelToPressH ? 'Aligned' : 'Misaligned',
      };
    }

    let winningCandidate = selectWinningCandidate(candidateA, candidateB, alignmentMode);

    if (!winningCandidate) {
      currentResult.error = true;
      currentResult.errorMessageKey = currentResult.errorMessageKey || "errorCannotFit";
      results.push(currentResult as CalculationResult);
      continue;
    }

    // Merge results from previous steps into the winning candidate
    winningCandidate = {
        ...initialFit,
        ...pressFit,
        ...currentResult,
        ...winningCandidate
    };

    // Finalize roll dimensions if applicable
    winningCandidate = finalizeRollDimensions(winningCandidate, gripperMm, colorBarMm, lipMm || 0);

    // Calculate final metrics based on component type
    const finalResult = calculateFinalMetrics_ComponentSpecific(
      componentType,
      winningCandidate,
      jobInputs,
      benchmarkValue
    );
    results.push(finalResult);
  }

  console.log('Finished calculations, results:', results);
  return results;
};


// --- Helper Functions ---

export const validatePaperOptionInputs = (paperData: PaperOptionData): PaperOptionData => {
  const validatedData = { ...paperData };
  validatedData.error = false;
  validatedData.errorMessageKey = '';

  const { source, sheetH_input, sheetW_input, caliperMicrons, costReam, gsm, costTonne } = validatedData;

  const numSheetH = safeParseFloat(sheetH_input, -1);
  const numSheetW = safeParseFloat(sheetW_input, -1);
  const numCaliper = safeParseFloat(caliperMicrons, -1);
  const numCostReam = safeParseFloat(costReam, -1);
  const numGsm = safeParseFloat(gsm, -1);
  const numCostTonne = safeParseFloat(costTonne, -1);

  if (numSheetH < 0 && source === 'Pre-Cut') { validatedData.error = true; validatedData.errorMessageKey = 'errorInvalidSheetDims'; }
  if (numSheetW < 0 && source === 'Pre-Cut') { validatedData.error = true; validatedData.errorMessageKey = 'errorInvalidSheetDims'; }

  if (source === 'Roll') {
    if ((sheetH_input !== undefined && numSheetH < 0) || (sheetW_input !== undefined && numSheetW < 0)) {
        validatedData.error = true; validatedData.errorMessageKey = 'alertNegativeProdParams';
    }
    if ((numSheetH > 0 && numSheetW < 0 && sheetW_input !== undefined) || (numSheetW > 0 && numSheetH < 0 && sheetH_input !== undefined)) {
        validatedData.error = true; validatedData.errorMessageKey = 'alertNegativeProdParams';
    }
  }

  if (numCaliper < 0) { validatedData.error = true; validatedData.errorMessageKey = 'alertNegativeProdParams'; }

  if (source === 'Pre-Cut') {
    if (numCostReam !== undefined && numCostReam <= 0) { validatedData.error = true; validatedData.errorMessageKey = 'errorMissingCostReam'; }
  } else if (source === 'Roll') {
    if (numGsm !== undefined && numGsm <= 0) { validatedData.error = true; validatedData.errorMessageKey = 'errorMissingRollData'; }
    if (numCostTonne !== undefined && numCostTonne < 0) { validatedData.error = true; validatedData.errorMessageKey = 'alertNegativeProdParams'; }
     if (numCostTonne !== undefined && numCostTonne <= 0 && numGsm !== undefined && numGsm > 0) {
         validatedData.error = true; validatedData.errorMessageKey = 'errorMissingRollData';
     }
  }
   if (gsm !== undefined && numGsm < 0) {
    validatedData.error = true; validatedData.errorMessageKey = 'alertNegativeProdParams';
  }

  validatedData.sheetH_input = numSheetH >= 0 ? numSheetH : 0;
  validatedData.sheetW_input = numSheetW >= 0 ? numSheetW : 0;
  validatedData.caliperMicrons = numCaliper >=0 ? numCaliper : 0;
  validatedData.costReam = numCostReam >=0 ? numCostReam : undefined;
  validatedData.gsm = numGsm >=0 ? numGsm : undefined;
  validatedData.costTonne = numCostTonne >=0 ? numCostTonne : undefined;

  return validatedData;
};


const safeParseFloat = (value: string | number | undefined, defaultValue = 0): number => {
  if (value === undefined || value === null || value === '') return defaultValue;
  const parsed = parseFloat(String(value));
  return isNaN(parsed) ? defaultValue : parsed;
};

const floor = Math.floor;
const ceil = Math.ceil;

export const calculateBleedDimensions_ComponentSpecific = (
  componentType: 'innerText' | 'cover' | 'endpapers',
  trimW: number,
  trimH: number,
  bleed: number,
  bindingMethod?: string, // Only for inner text
  coverType?: string, // Only for cover
  spineThicknessMm?: number, // Only for cover
  flapWidthMm?: number, // Only for cover
  turnInAllowanceMm?: number // Only for cover (hardcover)
): { layoutPageH: number; layoutPageW: number } | null => {

  if (trimW <= 0 || trimH <= 0 || bleed < 0) return null;

  let layoutPageH = 0;
  let layoutPageW = 0;

  if (componentType === 'innerText') {
    const fourSideBleedBindings = ['singlePage', 'wireO'];
    if (!bindingMethod) return null; // Binding method required for inner text

    if (fourSideBleedBindings.includes(bindingMethod)) {
      layoutPageH = trimH + 2 * bleed;
      layoutPageW = trimW + 2 * bleed;
    } else {
      layoutPageH = trimH + 2 * bleed;
      layoutPageW = trimW + bleed;
    }
  } else if (componentType === 'cover') {
      if (!coverType) return null; // Cover type required for cover
      const safeSpine = safeParseFloat(spineThicknessMm, 0);
      const safeFlap = safeParseFloat(flapWidthMm, 0);
      const safeTurnIn = safeParseFloat(turnInAllowanceMm, 0);

      if (coverType === 'paperback') {
          // Paperback cover: 3 panels (back, spine, front) + bleeds
          // Layout width = back + spine + front + 2*bleed
          // Layout height = cover height + 2*bleed
          layoutPageH = trimH + 2 * bleed;
          layoutPageW = (trimW * 2) + safeSpine + 2 * bleed;
      } else if (coverType === 'hardcover') {
          // Hardcover case: 2 boards + spine + turn-ins + bleeds
          // Layout width = board width * 2 + spine + 2*turn-in + 2*bleed
          // Layout height = board height + 2*turn-in + 2*bleed
          // Assuming board width = trimW, board height = trimH
           layoutPageH = trimH + 2 * bleed + 2 * safeTurnIn;
           layoutPageW = (trimW * 2) + safeSpine + 2 * bleed + 2 * safeTurnIn;
      } else if (coverType === 'dustJacket') {
          // Dust jacket: front flap + front + spine + back + back flap + bleeds
          // Layout width = front flap + front + spine + back + back flap + 2*bleed
          // Layout height = dust jacket height + 2*bleed
          layoutPageH = trimH + 2 * bleed;
          layoutPageW = (safeFlap * 2) + (trimW * 2) + safeSpine + 2 * bleed;
      } else {
          return null; // Unknown cover type
      }
  } else if (componentType === 'endpapers') {
      // Endpapers: typically a single folded sheet (4 pages) or two single sheets (4 pages) per book
      // Imposed as a flat sheet. Needs bleed on 3 sides (top, bottom, fore-edge)
      layoutPageH = trimH + 2 * bleed;
      layoutPageW = (trimW * 2) + bleed; // Assuming a folded sheet (4 pages)
  } else {
      return null; // Unknown component type
  }

  return layoutPageH > 0 && layoutPageW > 0 ? { layoutPageH, layoutPageW } : null;
};

export const determineOptimalLayoutBenchmark_ComponentSpecific = (
  componentType: 'innerText' | 'cover' | 'endpapers',
  maxPressH: number,
  maxPressW: number,
  gripper: number,
  colorBar: number,
  lip: number,
  bleed: number,
  trimW: number,
  trimH: number,
  jobInputs: InnerTextJobSpecs | CoverJobSpecs | EndpapersJobSpecs
): number => {

  const usableH = maxPressH - gripper - colorBar;
  const usableW = maxPressW - lip;

  if (usableH <= 0 || usableW <= 0) return 0;

  let dims: { layoutPageH: number; layoutPageW: number } | null = null;

  if (componentType === 'innerText') {
      const innerTextJobInputs = jobInputs as InnerTextJobSpecs;
      dims = calculateBleedDimensions_ComponentSpecific(
          componentType, trimW, trimH, bleed, innerTextJobInputs.bindingMethod
      );
  } else if (componentType === 'cover') {
      const coverJobInputs = jobInputs as CoverJobSpecs;
      dims = calculateBleedDimensions_ComponentSpecific(
          componentType, trimW, trimH, bleed, undefined, coverJobInputs.coverType, safeParseFloat(coverJobInputs.spineThicknessMm), safeParseFloat(coverJobInputs.flapWidthMm), safeParseFloat(coverJobInputs.turnInAllowanceMm) // Access from jobInputs
      );
  } else if (componentType === 'endpapers') {
       dims = calculateBleedDimensions_ComponentSpecific(
           componentType, trimW, trimH, bleed, undefined, undefined, undefined, undefined
       );
  } else {
      return 0; // Unknown component type
  }


  if (!dims || dims.layoutPageH <= 0 || dims.layoutPageW <= 0) return 0;

  const { layoutPageH, layoutPageW } = dims;

  let fitA = 0;
  const itemsDownA = floor(usableH / layoutPageH);
  let itemsAcrossA_initial = floor(usableW / layoutPageW);

  let itemsAcrossA = itemsAcrossA_initial;
  if (componentType === 'innerText') {
      const innerTextJobInputs = jobInputs as InnerTextJobSpecs;
      if (!['singlePage', 'wireO'].includes(innerTextJobInputs.bindingMethod)) {
          itemsAcrossA = itemsAcrossA_initial > 0 ? floor(itemsAcrossA_initial / 2) * 2 : 0;
      }
  }
  fitA = itemsDownA * itemsAcrossA;
  fitA = isNaN(fitA) ? 0 : fitA;

  let fitB = 0;
  const itemsDownB_initial = floor(usableH / layoutPageW);
  const itemsAcrossB = floor(usableW / layoutPageH);

  let itemsDownB = itemsDownB_initial;
  if (componentType === 'innerText') {
      const innerTextJobInputs = jobInputs as InnerTextJobSpecs;
      if (!['singlePage', 'wireO'].includes(innerTextJobInputs.bindingMethod)) {
          itemsDownB = itemsDownB_initial > 0 ? floor(itemsDownB_initial / 2) * 2 : 0;
      }
  }
  fitB = itemsDownB * itemsAcrossB;
  fitB = isNaN(fitB) ? 0 : fitB;

  // Benchmark is typically based on Inner Text imposition principles (signatures)
  // For Cover and Endpapers, the benchmark might simply be the maximum fit
  if (componentType === 'innerText') {
      return Math.min(Math.max(fitA, fitB), 16);
  } else {
      return Math.max(fitA, fitB);
  }
};

export const determineInitialPressFit = (
  paperData: PaperOptionData,
  maxPressH: number,
  maxPressW: number
): {
  sheetH_for_initial_fit: number;
  sheetW_for_initial_fit: number;
  inputNoteKey: string;
  rollDimensionOptimizedForFitting: boolean;
  error: boolean;
  errorMessageKey: string;
} => {
  let sheetH_for_initial_fit: number;
  let sheetW_for_initial_fit: number;
  let inputNoteKey = "";
  let rollDimensionOptimizedForFitting = false;
  let error = false;
  let errorMessageKey = "";

  const originalH = safeParseFloat(paperData.sheetH_input, 0);
  const originalW = safeParseFloat(paperData.sheetW_input, 0);

  if (paperData.source === 'Roll') {
    if (originalH > 0 && originalW > 0) {
      sheetH_for_initial_fit = originalH;
      sheetW_for_initial_fit = originalW;
      rollDimensionOptimizedForFitting = false;
    } else if (originalW > 0 && originalH <= 0) {
      sheetW_for_initial_fit = originalW;
      sheetH_for_initial_fit = Math.max(maxPressH, maxPressW);
      inputNoteKey = "resNoteHeightInferred";
      rollDimensionOptimizedForFitting = true;
    } else if (originalH > 0 && originalW <= 0) {
      sheetH_for_initial_fit = originalH;
      sheetW_for_initial_fit = Math.max(maxPressH, maxPressW);
      inputNoteKey = "resNoteWidthInferred";
      rollDimensionOptimizedForFitting = true;
    } else {
      error = true;
      errorMessageKey = "errorMissingRollData";
      sheetH_for_initial_fit = 0;
      sheetW_for_initial_fit = 0;
    }
  } else if (paperData.source === 'Pre-Cut') {
    sheetH_for_initial_fit = originalH;
    sheetW_for_initial_fit = originalW;
    if (sheetH_for_initial_fit <= 0 || sheetW_for_initial_fit <= 0) {
      error = true;
      errorMessageKey = "errorInvalidSheetDims";
    }
    rollDimensionOptimizedForFitting = false;
  } else {
    error = true;
    errorMessageKey = "errorUnknownSource";
    sheetH_for_initial_fit = 0;
    sheetW_for_initial_fit = 0;
  }

  if (!error && (sheetH_for_initial_fit <= 0 || sheetW_for_initial_fit <= 0)) {
    error = true;
    errorMessageKey = "errorInvalidSheetDims";
  }

  return {
    sheetH_for_initial_fit: error ? 0 : parseFloat(sheetH_for_initial_fit.toFixed(1)),
    sheetW_for_initial_fit: error ? 0 : parseFloat(sheetW_for_initial_fit.toFixed(1)),
    inputNoteKey,
    rollDimensionOptimizedForFitting,
    error,
    errorMessageKey,
  };
};

export const applyPressLimitsAndGetGrain = (
  initialH: number,
  initialW: number,
  maxPressH: number,
  maxPressW: number,
  grainDirInput: 'Height' | 'Width'
): {
  pressH: number;
  pressW: number;
  paperWasRotated: boolean;
  pressSizeNoteKey: string;
  isLongGrainParallelToPressH: boolean;
} => {
  let pressH = initialH;
  let pressW = initialW;
  let paperWasRotated = false;
  let pressSizeNoteKey = "";

  if (initialH <= maxPressH && initialW <= maxPressW) {
    pressH = initialH;
    pressW = initialW;
    paperWasRotated = false;
  }
  else if (initialW <= maxPressH && initialH <= maxPressW) {
    pressH = initialW;
    pressW = initialH;
    paperWasRotated = true;
    pressSizeNoteKey = "resNoteRotated";
  }
  else {
    // If neither fits directly, check if rotating helps fit within press limits
    if (initialH <= maxPressH && initialW <= maxPressW) {
        pressH = initialH; pressW = initialW; paperWasRotated = false;
    } else if (initialW <= maxPressH && initialH <= maxPressW) {
        pressH = initialW; pressW = initialH; paperWasRotated = true; pressSizeNoteKey = "resNoteRotated";
    } else {
        // If still doesn't fit, trim to press limits
        pressH = Math.min(initialH, maxPressH);
        pressW = Math.min(initialW, maxPressW);
        paperWasRotated = false; // Cannot be rotated if trimmed
        if (initialH > maxPressH || initialW > maxPressW) {
            pressSizeNoteKey = "resNoteTrimmed";
        }
    }
  }

  const isInputGrainParallelToInputH = grainDirInput === 'Height';

  let isLongGrainParallelToPressH: boolean;
  if (paperWasRotated) {
    isLongGrainParallelToPressH = !isInputGrainParallelToInputH;
  } else {
    isLongGrainParallelToPressH = isInputGrainParallelToInputH;
  }

  return {
    pressH: parseFloat(pressH.toFixed(1)),
    pressW: parseFloat(pressW.toFixed(1)),
    paperWasRotated,
    pressSizeNoteKey,
    isLongGrainParallelToPressH,
  };
};

export const calculateUsableArea = (
  pressH: number,
  pressW: number,
  gripper: number,
  colorBar: number,
  lip: number
): {
  usableH: number;
  usableW: number;
  error: boolean;
  errorMessageKey: string;
} => {
  const usableH = Math.max(0, pressH - gripper - colorBar);
  const usableW = Math.max(0, pressW - lip);

  let error = false;
  let errorMessageKey = "";
  if (usableH <= 0 || usableW <= 0) {
    error = true;
    errorMessageKey = "errorUsableAreaNegative";
  }

  return {
    usableH: parseFloat(usableH.toFixed(1)),
    usableW: parseFloat(usableW.toFixed(1)),
    error,
    errorMessageKey,
  };
};


interface LayoutFitResult {
  layoutDown: number;
  layoutAcross: number;
  fit: number;
  occupiedHeight: number;
  occupiedWidth: number;
}

export const calculateSingleLayoutFit_ComponentSpecific = (
  componentType: 'innerText' | 'cover' | 'endpapers',
  usableH: number,
  usableW: number,
  itemDimH: number,
  itemDimW: number,
  jobInputs: InnerTextJobSpecs | CoverJobSpecs | EndpapersJobSpecs
): LayoutFitResult | null => {
  if (itemDimH <= 0 || itemDimW <= 0 || usableH < itemDimH || usableW < itemDimW) {
    return null;
  }

  let layoutDown_initial = floor(usableH / itemDimH);
  let layoutAcross_initial = floor(usableW / itemDimW);

  let finalLayoutDown = 0;
  let finalLayoutAcross = 0;
  let bestFittingItems = 0;

  if (componentType === 'innerText') {
    const innerTextJobInputs = jobInputs as InnerTextJobSpecs;
    let currentLayoutDown1 = layoutDown_initial;
    let currentLayoutAcross1 = layoutAcross_initial;
    // Inner text imposition often requires pairs of pages (signatures)
    if (!['singlePage', 'wireO'].includes(innerTextJobInputs.bindingMethod)) {
        currentLayoutAcross1 = layoutAcross_initial > 0 ? floor(layoutAcross_initial / 2) * 2 : 0;
    }
    bestFittingItems = currentLayoutDown1 * currentLayoutAcross1;
    finalLayoutDown = currentLayoutDown1;
    finalLayoutAcross = currentLayoutAcross1;
  } else {
    // Covers and Endpapers are typically imposed as individual items or sets
    finalLayoutDown = layoutDown_initial;
    finalLayoutAcross = layoutAcross_initial;
    bestFittingItems = finalLayoutDown * finalLayoutAcross;
  }

  if (bestFittingItems <= 0) return null;

  const occupiedHeight = finalLayoutDown * itemDimH;
  const occupiedWidth = finalLayoutAcross * itemDimW;

  // Add a small tolerance for floating point comparisons
  if (occupiedHeight <= usableH + 0.01 && occupiedWidth <= usableW + 0.01) {
    return {
      layoutDown: finalLayoutDown,
      layoutAcross: finalLayoutAcross,
      fit: bestFittingItems,
      occupiedHeight,
      occupiedWidth,
    };
  }
  return null;
};


export const selectWinningCandidate = (
  candidateA: LayoutCandidate | null,
  candidateB: LayoutCandidate | null,
  requiredAlignmentMode: 'Aligned' | 'Misaligned'
): LayoutCandidate | null => {
  const validCandidates: LayoutCandidate[] = [];
  if (candidateA && candidateA.maxItemsPerSide !== undefined && candidateA.maxItemsPerSide > 0) validCandidates.push(candidateA);
  if (candidateB && candidateB.maxItemsPerSide !== undefined && candidateB.maxItemsPerSide > 0) validCandidates.push(candidateB);

  if (validCandidates.length === 0) return null;

  const matchingAlignmentCandidates = validCandidates.filter(
    (c) => c.grainAlignmentStatus === requiredAlignmentMode
  );

  if (matchingAlignmentCandidates.length === 0) {
      // If no candidates match the required alignment, return the best fit regardless of alignment
      validCandidates.sort((x, y) => {
        if (x.maxItemsPerSide !== y.maxItemsPerSide) {
          return y.maxItemsPerSide - x.maxItemsPerSide;
        }
        const usableAreaX = (x.usableH || 0) * (x.usableW || 0);
        const usableAreaY = (y.usableH || 0) * (y.usableW || 0);
        const wasteX = usableAreaX - ((x.occupiedHeight || 0) * (x.occupiedWidth || 0));
        const wasteY = usableAreaY - ((y.occupiedHeight || 0) * (y.occupiedWidth || 0));
        return wasteX - wasteY;
      });
      return validCandidates[0];
  }

  matchingAlignmentCandidates.sort((x, y) => {
    if (x.maxItemsPerSide !== y.maxItemsPerSide) {
      return y.maxItemsPerSide - x.maxItemsPerSide;
    }
    const usableAreaX = (x.usableH || 0) * (x.usableW || 0);
    const usableAreaY = (y.usableH || 0) * (y.usableW || 0);
    const wasteX = usableAreaX - ((x.occupiedHeight || 0) * (x.occupiedWidth || 0));
    const wasteY = usableAreaY - ((y.occupiedHeight || 0) * (y.occupiedWidth || 0));
    return wasteX - wasteY;
  });

  return matchingAlignmentCandidates[0];
};

export const finalizeRollDimensions = (
  candidate: LayoutCandidate,
  gripper: number,
  colorBar: number,
  lip: number
): LayoutCandidate => {
  const updatedCandidate = { ...candidate };

  if (
    updatedCandidate.source !== 'Roll' ||
    !updatedCandidate.rollDimensionOptimizedForFitting ||
    updatedCandidate.error ||
    updatedCandidate.pressH === undefined ||
    updatedCandidate.pressW === undefined ||
    updatedCandidate.occupiedHeight === undefined ||
    updatedCandidate.occupiedWidth === undefined
  ) {
    return updatedCandidate;
  }

  const totalVerticalMargins = gripper + colorBar;
  const totalHorizontalMargins = lip;

  let finalPressH = updatedCandidate.pressH;
  let finalPressW = updatedCandidate.pressW;

  if (updatedCandidate.inputNoteKey === "resNoteHeightInferred") {
    if (updatedCandidate.paperWasRotated) {
      finalPressW = updatedCandidate.occupiedWidth + totalHorizontalMargins;
    } else {
      finalPressH = updatedCandidate.occupiedHeight + totalVerticalMargins;
    }
  } else if (updatedCandidate.inputNoteKey === "resNoteWidthInferred") {
    if (updatedCandidate.paperWasRotated) {
      finalPressH = updatedCandidate.occupiedHeight + totalVerticalMargins;
    } else {
      finalPressW = updatedCandidate.occupiedWidth + totalHorizontalMargins;
    }
  } else {
    return updatedCandidate;
  }

  updatedCandidate.pressH = parseFloat(finalPressH.toFixed(1));
  updatedCandidate.pressW = parseFloat(finalPressW.toFixed(1));

  const newUsableH = Math.max(0, updatedCandidate.pressH - totalVerticalMargins);
  const newUsableW = Math.max(0, updatedCandidate.pressW - totalHorizontalMargins);
  updatedCandidate.usableH = parseFloat(newUsableH.toFixed(1));
  updatedCandidate.usableW = parseFloat(newUsableW.toFixed(1));

  updatedCandidate.rollOptimizationNoteKey = "rollDimensionsOptimized";

  return updatedCandidate;
};

export const calculateFinalMetrics_ComponentSpecific = (
  componentType: 'innerText' | 'cover' | 'endpapers',
  candidate: LayoutCandidate,
  jobInputs: InnerTextJobSpecs | CoverJobSpecs | EndpapersJobSpecs,
  benchmarkValue: number
): CalculationResult => {
  const updatedCandidate = { ...candidate } as CalculationResult;

  try {
    if (updatedCandidate.error || updatedCandidate.maxItemsPerSide === undefined || updatedCandidate.maxItemsPerSide <= 0) {
      throw new Error("Cannot calculate final metrics due to earlier error or no fit.");
    }

    const quantity = safeParseFloat(jobInputs.quantity);
    const spoilagePct = safeParseFloat(jobInputs.spoilagePercent) / 100;

    if (componentType === 'innerText') {
      updatedCandidate.pagesPerSheetOutput = updatedCandidate.maxItemsPerSide * 2;
      if (updatedCandidate.pagesPerSheetOutput <= 0) throw new Error("Invalid pages per sheet output for Inner Text.");

      const totalPages = safeParseFloat((jobInputs as InnerTextJobSpecs).totalPages);
      if (totalPages <= 0) throw new Error("Invalid total pages for Inner Text.");

      updatedCandidate.totalSheetsNeeded = ceil(
        ceil(totalPages / updatedCandidate.pagesPerSheetOutput) * quantity * (1 + spoilagePct)
      );
       if (updatedCandidate.caliperMicrons !== undefined && updatedCandidate.caliperMicrons > 0 && safeParseFloat((jobInputs as InnerTextJobSpecs).totalPages) > 0) {
          updatedCandidate.bookBlockThickness_mm = (safeParseFloat((jobInputs as InnerTextJobSpecs).totalPages) / 2) * (updatedCandidate.caliperMicrons / 1000);
        } else {
          updatedCandidate.bookBlockThickness_mm = undefined;
        }
    } else if (componentType === 'cover') {
        updatedCandidate.pagesPerSheetOutput = updatedCandidate.maxItemsPerSide;
        updatedCandidate.totalSheetsNeeded = ceil(
           quantity / updatedCandidate.pagesPerSheetOutput * (1 + spoilagePct)
        );
        updatedCandidate.costPerCover = updatedCandidate.totalCost !== undefined && quantity > 0 ? updatedCandidate.totalCost / quantity : undefined;
    } else if (componentType === 'endpapers') {
         updatedCandidate.pagesPerSheetOutput = updatedCandidate.maxItemsPerSide;
         updatedCandidate.totalSheetsNeeded = ceil(
            quantity / updatedCandidate.pagesPerSheetOutput * (1 + spoilagePct)
         );
         updatedCandidate.costPerEndpaperSet = updatedCandidate.totalCost !== undefined && quantity > 0 ? updatedCandidate.totalCost / quantity : undefined;
    }


    if (updatedCandidate.totalSheetsNeeded === undefined || updatedCandidate.totalSheetsNeeded <= 0 || isNaN(updatedCandidate.totalSheetsNeeded)) {
        throw new Error("Invalid total sheets needed.");
    }

    if (updatedCandidate.source === "Pre-Cut") {
      if (updatedCandidate.costReam === undefined || updatedCandidate.costReam <= 0) throw new Error("Invalid Cost/Ream for Pre-Cut.");
      updatedCandidate.costPerSheet = updatedCandidate.costReam / 500;
    } else {
      if (updatedCandidate.gsm === undefined || updatedCandidate.gsm <= 0 || updatedCandidate.costTonne === undefined || updatedCandidate.costTonne < 0) {
        throw new Error("Invalid GSM or Cost/Tonne for Roll.");
      }
      if(updatedCandidate.pressW === undefined || updatedCandidate.pressH === undefined) throw new Error("Press dimensions missing for roll cost calculation.");
      const areaM2 = (updatedCandidate.pressW / 1000) * (updatedCandidate.pressH / 1000);
      if (areaM2 <= 0) throw new Error("Invalid press area for roll cost calculation.");
      const weightKg = areaM2 * updatedCandidate.gsm / 1000;
      updatedCandidate.costPerSheet = (weightKg * updatedCandidate.costTonne / 1000);
    }

    if (updatedCandidate.costPerSheet === undefined || updatedCandidate.costPerSheet <= 0 || isNaN(updatedCandidate.costPerSheet)) {
      throw new Error("Invalid cost per sheet.");
    }
    updatedCandidate.totalCost = updatedCandidate.totalSheetsNeeded * updatedCandidate.costPerSheet;

    if (quantity > 0) {
      if (componentType === 'innerText') {
        updatedCandidate.costPerBook = updatedCandidate.totalCost / quantity;
        if (updatedCandidate.caliperMicrons !== undefined && updatedCandidate.caliperMicrons > 0 && safeParseFloat((jobInputs as InnerTextJobSpecs).totalPages) > 0) {
          updatedCandidate.bookBlockThickness_mm = (safeParseFloat((jobInputs as InnerTextJobSpecs).totalPages) / 2) * (updatedCandidate.caliperMicrons / 1000);
        } else {
          updatedCandidate.bookBlockThickness_mm = undefined;
        }
      } else if (componentType === 'cover') {
          updatedCandidate.costPerCover = updatedCandidate.totalCost / quantity;
      } else if (componentType === 'endpapers') {
          updatedCandidate.costPerEndpaperSet = updatedCandidate.totalCost / quantity;
      }
    } else {
      if (componentType === 'innerText') updatedCandidate.costPerBook = undefined;
      if (componentType === 'cover') updatedCandidate.costPerCover = undefined;
      if (componentType === 'endpapers') updatedCandidate.costPerEndpaperSet = undefined;
    }

    if (isNaN(updatedCandidate.totalCost) || updatedCandidate.totalCost === Infinity || updatedCandidate.totalCost === undefined) {
         throw new Error("Invalid final total cost calculation.");
     }

    const finalUsableArea = (updatedCandidate.usableH || 0) * (updatedCandidate.usableW || 0);
    const occupiedArea = (updatedCandidate.occupiedHeight || 0) * (updatedCandidate.occupiedWidth || 0);
    if (finalUsableArea > 0 && occupiedArea <= finalUsableArea) {
      updatedCandidate.wastePercent = Math.max(0, (finalUsableArea - occupiedArea)) / finalUsableArea;
    } else {
      updatedCandidate.wastePercent = 1.0; // Indicate full waste if occupied area is larger or usable area is zero
    }
    updatedCandidate.utilisationRate = 1.0 - updatedCandidate.wastePercent;

    updatedCandidate.isOptimalCandidate = (updatedCandidate.maxItemsPerSide === benchmarkValue && !updatedCandidate.error);

  } catch (error: any) {
    console.error(`Final Metrics error for ${componentType}, paper ${updatedCandidate.id}:`, error.message);
    updatedCandidate.error = true;
    updatedCandidate.errorMessageKey = updatedCandidate.errorMessageKey ||
        (error.message.includes("Cost/Ream") ? "errorMissingCostReam" :
         error.message.includes("GSM or Cost/Tonne") ? "errorMissingRollData" :
         error.message.includes("cost per sheet") ? "errorCostSheetNegative" :
         "errorCostCalc");
    updatedCandidate.totalCost = Infinity;
    updatedCandidate.costPerSheet = undefined;
    if (componentType === 'innerText') updatedCandidate.costPerBook = undefined;
    if (componentType === 'cover') updatedCandidate.costPerCover = undefined;
    if (componentType === 'endpapers') updatedCandidate.costPerEndpaperSet = undefined;
    updatedCandidate.bookBlockThickness_mm = undefined;
    updatedCandidate.wastePercent = 1.1;
    updatedCandidate.utilisationRate = -0.1;
  }
  return updatedCandidate;
};


// Constants for press limits (can be moved to a config file or store later)
export const MAX_PRESS_H = 720;
export const MAX_PRESS_W = 1020;
