export const MM_TO_IN_FACTOR = 1 / 25.4;
export const IN_TO_MM_FACTOR = 25.4;

export const PT_TO_MICRONS_FACTOR = (25.4 / 72) * 1000; // Points to Microns
export const MICRONS_TO_PT_FACTOR = 1 / PT_TO_MICRONS_FACTOR;

// Paper weight conversion factors (approximate, from original code)
const LBS_GSM_FACTORS = {
  text: 1.48,
  cover: 2.70,
};

export type PaperWeightType = 'Text' | 'Cover';

export const safeParseFloat = (value: string | number, defaultValue = 0): number => {
  const parsed = parseFloat(String(value));
  return isNaN(parsed) ? defaultValue : parsed;
};

export const mmToIn = (mm: string | number): string => {
  const num = safeParseFloat(mm, NaN);
  return isNaN(num) ? '' : (num * MM_TO_IN_FACTOR).toFixed(3); // Original used toFixed(3)
};

export const inToMm = (inches: string | number): string => {
  const num = safeParseFloat(inches, NaN);
  return isNaN(num) ? '' : (num * IN_TO_MM_FACTOR).toFixed(1); // Original used toFixed(1)
};

export const ptToMicrons = (pt: string | number): string => {
  const num = safeParseFloat(pt, NaN);
  return isNaN(num) ? '' : (num * PT_TO_MICRONS_FACTOR).toFixed(1); // Original used toFixed(1)
};

export const micronsToPt = (microns: string | number): string => {
  const num = safeParseFloat(microns, NaN);
  return isNaN(num) ? '' : (num * MICRONS_TO_PT_FACTOR).toFixed(2); // Original used toFixed(2)
};

export const lbsToGsm = (lbs: string | number, type: PaperWeightType): string => {
  const num = safeParseFloat(lbs, NaN);
  const factor = type === 'Text' ? LBS_GSM_FACTORS.text : LBS_GSM_FACTORS.cover;
  return isNaN(num) ? '' : (num * factor).toFixed(1); // Original used toFixed(1)
};

export const gsmToLbs = (gsm: string | number, type: PaperWeightType): string => {
  const num = safeParseFloat(gsm, NaN);
  const factor = type === 'Text' ? LBS_GSM_FACTORS.text : LBS_GSM_FACTORS.cover;
  return isNaN(num) ? '' : (num / factor).toFixed(1); // Original used toFixed(1)
};
