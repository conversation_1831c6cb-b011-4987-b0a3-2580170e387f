import React from 'react';
import JobSpecificationsCard from './JobSpecificationsCard';
import UnitConverterCard from './UnitConverterCard';
import ProductionParametersCard from './ProductionParametersCard';

const CalculatorLayout = () => {
  // Using flexbox for more reliable side-by-side layout
  return (
    <div className="container mx-auto px-4 py-6">
      {/* Tab navigation - preserved from original design */}
      <div className="flex mb-6 space-x-2">
        <button className="px-4 py-1 rounded-md bg-gray-200 font-medium">Inner Text</button>
        <button className="px-4 py-1 rounded-md bg-gray-100">Cover</button>
        <button className="px-4 py-1 rounded-md bg-gray-100">Endpapers</button>
      </div>
      
      {/* Card container with flex layout that will wrap on smaller screens */}
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="flex-1 min-w-0">
          <JobSpecificationsCard />
        </div>
        <div className="flex-1 min-w-0">
          <UnitConverterCard />
        </div>
        <div className="flex-1 min-w-0">
          <ProductionParametersCard />
        </div>
      </div>
    </div>
  );
};

export default CalculatorLayout; 