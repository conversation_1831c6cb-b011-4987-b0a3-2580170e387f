'use client';

import React from 'react';
import { useCoverStore, CoverProdParams } from '@/stores/coverStore';
import Card<PERSON>rapper from '@/components/ui/CardWrapper';

const CoverProductionParametersCard = () => {
  const { prodParams, setProdParam, toggleAlignmentMode } = useCoverStore();
  const { jobSpecs } = useCoverStore(); // Need jobSpecs to access turnInAllowanceMm

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: keyof CoverProdParams
  ) => {
    setProdParam(field, e.target.value);
  };

  return (
    <CardWrapper>
      <div className='card-header'>
        <h2 className='text-xl font-semibold' data-translate-key='coverProdParamsTitle'>
          Cover Prod. Parameters
        </h2>
      </div>
      <div className="space-y-4">
        {/* Standardized 2-column grid for input groups */}
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
          {/* Bleed */}
          <div>
            <label htmlFor='bleedMm' className='form-label' data-translate-key='bleedLabel'>
              Bleed (mm)
            </label>
            <input
              type='number'
              id='bleedMm'
              className='form-input'
              value={prodParams.bleedMm}
              onChange={(e) => handleInputChange(e, 'bleedMm')}
              placeholder='e.g., 5'
              data-placeholder-translate-key='placeholderBleed'
            />
          </div>

          {/* Gripper */}
          <div>
            <label htmlFor='gripperMm' className='form-label' data-translate-key='gripperLabel'>
              Gripper (mm)
            </label>
            <input
              type='number'
              id='gripperMm'
              className='form-input'
              value={prodParams.gripperMm}
              onChange={(e) => handleInputChange(e, 'gripperMm')}
              placeholder='e.g., 12'
              data-placeholder-translate-key='placeholderGripper'
            />
          </div>
        </div>

        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
          {/* Color Bar */}
          <div>
            <label htmlFor='colorBarMm' className='form-label' data-translate-key='colorBarLabel'>
              Color Bar (mm)
            </label>
            <input
              type='number'
              id='colorBarMm'
              className='form-input'
              value={prodParams.colorBarMm}
              onChange={(e) => handleInputChange(e, 'colorBarMm')}
              placeholder='e.g., 6'
              data-placeholder-translate-key='placeholderColorBar'
            />
          </div>

          {/* Turn-in Allowance Section - Now visible in 2-column layout */}
          <div id='turn-in-allowance-section_cover'>
              <label htmlFor='turnInAllowanceMm' className='form-label' data-translate-key='turnInAllowanceLabel'>
                  Turn-in Allowance (mm)
              </label>
              <input
                  type='number'
                  id='turnInAllowanceMm'
                  className='form-input'
                  value={jobSpecs.turnInAllowanceMm} // Note: This comes from CoverJobSpecs, not ProdParams
                  onChange={(e) => useCoverStore.getState().setJobSpec('turnInAllowanceMm', e.target.value)} // Update JobSpecs directly
                  step='0.1'
                  placeholder='e.g., 18'
                  data-placeholder-translate-key='placeholderTurnInAllowance'
              />
              <p className="text-xs text-neutral-500 mt-1 dark-mode-text" data-translate-key="turnInAllowanceNote">Standard is 15-20mm for hardcover turn-ins.</p>
          </div>
        </div>

        {/* Grain Alignment */}
        <div>
          <label className='form-label' data-translate-key='alignmentModeLabel'>
            Grain Alignment
          </label>
          <div className='flex items-center'>
            <div
              id='alignment-mode-switcher_cover'
              className={`alignment-switch ${prodParams.alignmentMode === 'Misaligned' ? 'misaligned' : ''}`}
              title='Switch Grain Alignment'
              onClick={toggleAlignmentMode}
              onKeyDown={(e) => e.key === 'Enter' && toggleAlignmentMode()}
              role='switch'
              aria-checked={prodParams.alignmentMode === 'Aligned'}
            >
              <div className='alignment-switch-toggle'></div>
            </div>
            <span className='ml-2 text-sm'>
              <span className={`alignment-mode-text misaligned-text ${prodParams.alignmentMode === 'Misaligned' ? '' : 'hidden'}`} data-translate-key='alignmentModeMisaligned'>
                Misaligned
              </span>
              <span className={`alignment-mode-text aligned-text ${prodParams.alignmentMode === 'Aligned' ? '' : 'hidden'}`} data-translate-key='alignmentModeAligned'>
                Aligned
              </span>
            </span>
            <span className='ml-2 text-neutral-500' title='Grain typically || to spine/cover height' data-title-translate-key='coverGrainNote'>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </span>
          </div>
        </div>
      </div>

      <p className='dark-mode-text mt-auto text-xs text-neutral-500' data-translate-key='prodParamsNote'>
        * Gutter not used for sheet area. Margins applied to 720H x 1020W press.
      </p>
    </CardWrapper>
  );
};

export default CoverProductionParametersCard;
