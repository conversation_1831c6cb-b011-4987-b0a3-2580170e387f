'use client';

import React, { useState, useEffect } from 'react';
import { CalculationResult } from '@/utils/calculationEngine';
import ResultCard from './ResultCard';
import { motion, AnimatePresence } from 'framer-motion';

interface ResultsSectionProps {
  results: CalculationResult[];
  isLoading?: boolean;
  // TODO: Add translation function or context for labels
}

const ResultsSection: React.FC<ResultsSectionProps> = ({ results, isLoading = false }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [bestOptionIndex, setBestOptionIndex] = useState<number | null>(null);
  const [selectedResults, setSelectedResults] = useState<Set<string>>(new Set());

  // Find the best option based on lowest waste percentage or lowest cost
  useEffect(() => {
    if (!results || results.length === 0) {
      setBestOptionIndex(null);
      return;
    }

    // Filter out results with errors
    const validResults = results.filter(r => !r.error);
    if (validResults.length === 0) {
      setBestOptionIndex(null);
      return;
    }

    // Find the result with the lowest waste percentage
    let lowestWasteIndex = 0;
    let lowestWastePercent = validResults[0].wastePercent || 1;

    validResults.forEach((result, index) => {
      if (result.wastePercent !== undefined && result.wastePercent < lowestWastePercent) {
        lowestWastePercent = result.wastePercent;
        lowestWasteIndex = index;
      }
    });

    // Map back to the original results array index
    const originalIndex = results.findIndex(r => r === validResults[lowestWasteIndex]);
    setBestOptionIndex(originalIndex);
  }, [results]);

  // Handle result selection
  const handleResultSelect = (result: CalculationResult) => {
    const resultId = `${result.id}-${result.paperName}`;
    setSelectedResults(prev => {
      const newSet = new Set(prev);
      if (newSet.has(resultId)) {
        newSet.delete(resultId);
      } else {
        newSet.add(resultId);
      }
      return newSet;
    });
  };

  // Check if a result is selected
  const isResultSelected = (result: CalculationResult) => {
    const resultId = `${result.id}-${result.paperName}`;
    return selectedResults.has(resultId);
  };

  // If no results or loading, show placeholder
  if (isLoading) {
    return (
      <div className="results-section-loading">
        <div className="loading-spinner"></div>
        <p>Calculating optimal layouts...</p>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        className="results-section"
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        exit={{ opacity: 0, height: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="results-header">
          <div>
            <h2 className="text-xl font-bold mb-1">Calculation Results</h2>
            {selectedResults.size > 0 && (
              <p className="text-sm text-primary font-medium">
                {selectedResults.size} option{selectedResults.size !== 1 ? 's' : ''} selected
              </p>
            )}
          </div>
          <div className="flex gap-3">
            {selectedResults.size > 0 && (
              <button
                onClick={() => setSelectedResults(new Set())}
                className="text-sm text-neutral-600 hover:text-neutral-800 dark:text-neutral-400 dark:hover:text-neutral-200 transition-colors"
              >
                Clear Selection
              </button>
            )}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="text-sm text-primary hover:text-primary-dark transition-colors"
            >
              {isCollapsed ? 'Show All Options' : 'Show Best Option Only'}
            </button>
          </div>
        </div>

        <div className="results-grid-container">
          <div className="results-grid">
            {isCollapsed && bestOptionIndex !== null ? (
              // Show only the best option when collapsed
              <ResultCard
                key={`result-${bestOptionIndex}`}
                result={results[bestOptionIndex]}
                isBestOption={true}
                isSelected={isResultSelected(results[bestOptionIndex])}
                onSelect={handleResultSelect}
              />
            ) : (
              // Show all options
              results.map((result, index) => (
                <ResultCard
                  key={`result-${index}`}
                  result={result}
                  isBestOption={index === bestOptionIndex}
                  isSelected={isResultSelected(result)}
                  onSelect={handleResultSelect}
                />
              ))
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ResultsSection;
