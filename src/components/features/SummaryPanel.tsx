'use client';

import React from 'react';
import { useSummary } from '@/contexts/SummaryContext';
import { useLanguage } from '@/contexts/LanguageContext';

export default function SummaryPanel() {
  const { summaryItems, isPanelVisible, togglePanel, removeFromSummary, totalCost } = useSummary();
  const { t } = useLanguage();

  const formatMmIn = (mm?: number) => {
    if (mm === undefined || isNaN(mm)) return 'N/A';
    const inches = (mm / 25.4).toFixed(2);
    return `${mm.toFixed(1)}mm (${inches}")`;
  };

  return (
    <div id="summary-panel" className={`${isPanelVisible ? 'visible' : ''}`}>
      <div id="summary-panel-header">
        <h3 className="text-lg font-semibold summary-static-title">
          {t('summaryTitle', 'Selected Components')}
        </h3>
        <button 
          id="summary-close-button" 
          title="Close Summary Panel"
          onClick={togglePanel}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div id="summary-content">
        {summaryItems.length === 0 ? (
          <p className="text-sm text-neutral-500 dark:text-neutral-400">
            {t('summaryNoSelection', 'No components selected yet.')}
          </p>
        ) : (
          summaryItems.map((item) => {
            const { result, componentType, id } = item;
            
            // Component type label
            const componentLabel = t(componentType, componentType === 'innerText' ? 'Inner Text' : componentType === 'cover' ? 'Cover' : 'Endpapers');
            
            // Format values
            const sourceText = result.source || 'N/A';
            const grainText = result.grainDirInput || 'N/A';
            const gsmText = result.gsm ? `${result.gsm}g/m²` : 'N/A';
            const caliperText = result.caliperMicrons ? `${result.caliperMicrons}µm` : 'N/A';
            const costValue = result.costPerBook || 0;
            
            return (
              <div key={`${componentType}-${id}`} className="summary-item">
                <div className="flex justify-between items-start">
                  <div className="summary-item-label font-semibold text-base mb-1">{componentLabel}</div>
                  <button 
                    className="text-neutral-500 hover:text-danger p-1 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors"
                    title="Remove item"
                    onClick={() => removeFromSummary(id)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                {/* Paper Details */}
                <div className="summary-item-label text-xs font-semibold mt-2 mb-0.5">
                  {t('summaryPaperDetails', 'Paper Details')}
                </div>
                <div className="summary-item-value font-medium mb-1 text-sm">{result.paperName}</div>
                <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs mb-2">
                  <div><span className="font-medium">{t('summarySource', 'Source')}:</span> {sourceText}</div>
                  <div><span className="font-medium">{t('summaryGrain', 'Grain')}:</span> {grainText}</div>
                  <div><span className="font-medium">{t('summaryGsm', 'GSM')}:</span> {gsmText}</div>
                  <div><span className="font-medium">{t('summaryCaliper', 'Caliper')}:</span> {caliperText}</div>
                </div>
                
                {/* Cost */}
                <div className="summary-item-value text-sm font-semibold mt-1">
                  {t('summaryCostPerBook', 'Cost/Book')}: ${costValue.toFixed(2)}
                </div>
              </div>
            );
          })
        )}
      </div>
      
      <div className="summary-total">
        <div className="flex justify-between items-center">
          <span className="summary-total-label">
            {t('summaryTotalCostPerBook', 'Total Cost/Book')}:
          </span>
          <span className="summary-total-value">${totalCost.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}
