'use client'; // Required for <PERSON>ust<PERSON> to work in App Router components

import React, { useEffect } from 'react';
import { useInnerTextStore, InnerTextJobSpecs } from '@/stores/innerTextStore';
import CardWrapper from '@/components/ui/CardWrapper';

const mmToIn = (mm: number | string): string => {
  const num = parseFloat(String(mm));
  return isNaN(num) ? '' : (num / 25.4).toFixed(2);
};

const inToMm = (inch: number | string): string => {
  const num = parseFloat(String(inch));
  return isNaN(num) ? '' : (num * 25.4).toFixed(1);
};

const JobSpecificationsCard = () => {
  const { jobSpecs, setJobSpec } = useInnerTextStore();

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    field: keyof InnerTextJobSpecs
  ) => {
    let value = e.target.value;
    setJobSpec(field, value);

    // Synchronize mm/inch fields
    if (field === 'trimHeightMm') {
      setJobSpec('trimHeightIn', mmToIn(value));
    } else if (field === 'trimHeightIn') {
      setJobSpec('trimHeightMm', inToMm(value));
    } else if (field === 'trimWidthMm') {
      setJobSpec('trimWidthIn', mmToIn(value));
    } else if (field === 'trimWidthIn') {
      setJobSpec('trimWidthMm', inToMm(value));
    }
  };

  // Effect to initialize inch values if mm values are present but inch values are empty
  useEffect(() => {
    if (jobSpecs.trimHeightMm && !jobSpecs.trimHeightIn) {
      setJobSpec('trimHeightIn', mmToIn(jobSpecs.trimHeightMm));
    }
    if (jobSpecs.trimWidthMm && !jobSpecs.trimWidthIn) {
      setJobSpec('trimWidthIn', mmToIn(jobSpecs.trimWidthMm));
    }
  }, [jobSpecs.trimHeightMm, jobSpecs.trimWidthMm, jobSpecs.trimHeightIn, jobSpecs.trimWidthIn, setJobSpec]);

  return (
    <CardWrapper>
      <div className='card-header'>
        <h2 className='text-xl font-semibold' data-translate-key='jobSpecsTitle'>
          Job Specifications
        </h2>
      </div>

      <div className="space-y-4">
        {/* Height measurements with conversion icon */}
        <div>
          <div className='mb-1 grid grid-cols-2 gap-x-4'>
            <label htmlFor="trimHeightMm" className="form-label" data-translate-key="pageHeightMmLabel">
              Page Height (mm)
            </label>
            <label htmlFor="trimHeightIn" className="form-label" data-translate-key="pageHeightInLabel">
              Page Height (in)
            </label>
          </div>
          <div className='flex items-center gap-3'>
            <div className='relative flex-1'>
              <input
                type="number"
                id="trimHeightMm"
                className="form-input pr-8"
                value={jobSpecs.trimHeightMm}
                onChange={(e) => handleInputChange(e, 'trimHeightMm')}
                step="0.1"
                placeholder="e.g., 210"
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>mm</span>
            </div>
            <div className='conversion-icon'>
              <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth='2' d='M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4'></path>
              </svg>
            </div>
            <div className='relative flex-1'>
              <input
                type="number"
                id="trimHeightIn"
                className="form-input pr-8"
                value={jobSpecs.trimHeightIn}
                onChange={(e) => handleInputChange(e, 'trimHeightIn')}
                step="0.01"
                placeholder="e.g., 8.27"
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>in</span>
            </div>
          </div>
        </div>

        {/* Width measurements with conversion icon */}
        <div>
          <div className='mb-1 grid grid-cols-2 gap-x-4'>
            <label htmlFor="trimWidthMm" className="form-label" data-translate-key="pageWidthMmLabel">
              Page Width (mm)
            </label>
            <label htmlFor="trimWidthIn" className="form-label" data-translate-key="pageWidthInLabel">
              Page Width (in)
            </label>
          </div>
          <div className='flex items-center gap-3'>
            <div className='relative flex-1'>
              <input
                type="number"
                id="trimWidthMm"
                className="form-input pr-8"
                value={jobSpecs.trimWidthMm}
                onChange={(e) => handleInputChange(e, 'trimWidthMm')}
                step="0.1"
                placeholder="e.g., 148"
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>mm</span>
            </div>
            <div className='conversion-icon'>
              <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth='2' d='M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4'></path>
              </svg>
            </div>
            <div className='relative flex-1'>
              <input
                type="number"
                id="trimWidthIn"
                className="form-input pr-8"
                value={jobSpecs.trimWidthIn}
                onChange={(e) => handleInputChange(e, 'trimWidthIn')}
                step="0.01"
                placeholder="e.g., 5.83"
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>in</span>
            </div>
          </div>
        </div>

        {/* Two-column layout for pages and quantity */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="totalPages" className="form-label" data-translate-key="totalPagesLabel">
              Total Pages
            </label>
            <input
              type="number"
              id="totalPages"
              className="form-input"
              value={jobSpecs.totalPages}
              onChange={(e) => handleInputChange(e, 'totalPages')}
              step="2"
              placeholder="e.g., 320"
            />
          </div>
          <div>
            <label htmlFor="quantity" className="form-label" data-translate-key="quantityLabel">
              Quantity
            </label>
            <input
              type="number"
              id="quantity"
              className="form-input"
              value={jobSpecs.quantity}
              onChange={(e) => handleInputChange(e, 'quantity')}
              placeholder="e.g., 1000"
            />
          </div>
        </div>

        {/* Full-width fields */}
        <div>
          <label htmlFor="bindingMethod" className="form-label" data-translate-key="bindingMethodLabel">
            Binding Method
          </label>
          <select
            id="bindingMethod"
            className="form-select"
            value={jobSpecs.bindingMethod}
            onChange={(e) => handleInputChange(e, 'bindingMethod')}
          >
            <option value="saddleStitch">Saddle Stitch</option>
            <option value="perfectBound">Paperback</option>
            <option value="wireO">Wire-O / Coil</option>
            <option value="sectionSewn">Section Sewn</option>
            <option value="caseBound">Case Bound (Hardcover)</option>
            <option value="singlePage">Single Page / Flat Sheet</option>
          </select>
        </div>

        <div>
          <label htmlFor="spoilagePercent" className="form-label" data-translate-key="spoilageLabel">
            Spoilage (%)
          </label>
          <input
            type="number"
            id="spoilagePercent"
            className="form-input"
            value={jobSpecs.spoilagePercent}
            onChange={(e) => handleInputChange(e, 'spoilagePercent')}
            step="0.1"
            placeholder="e.g., 5"
          />
        </div>
      </div>
    </CardWrapper>
  );
};

export default JobSpecificationsCard;
