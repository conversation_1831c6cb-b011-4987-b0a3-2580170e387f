'use client';

import React, { useEffect } from 'react';
import { useEndpapersStore, EndpapersJobSpecs } from '@/stores/endpapersStore';
import { useInnerTextStore } from '@/stores/innerTextStore'; // To potentially auto-fill from Inner Text
import { mmToIn, inToMm } from '@/utils/conversions'; // Assuming these are in conversions.ts
import CardWrapper from '@/components/ui/CardWrapper';

const EndpapersSpecificationsCard = () => {
  const { jobSpecs, setJobSpec } = useEndpapersStore();
  const { jobSpecs: innerTextJobSpecs } = useInnerTextStore(); // Get Inner Text job specs

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    field: keyof EndpapersJobSpecs
  ) => {
    let value = e.target.value;
    setJobSpec(field, value);

    // Synchronize mm/inch fields
    if (field === 'trimHeightMm') {
      setJobSpec('trimHeightIn', mmToIn(value));
    } else if (field === 'trimHeightIn') {
      setJobSpec('trimHeightMm', inToMm(value));
    } else if (field === 'trimWidthMm') {
      setJobSpec('trimWidthIn', mmToIn(value));
    } else if (field === 'trimWidthIn') {
      setJobSpec('trimWidthMm', inToMm(value));
    }
  };

  // Effect to auto-fill from Inner Text if fields are empty
  useEffect(() => {
    // Only auto-fill if Inner Text has valid dimensions and quantity
    const innerH = parseFloat(innerTextJobSpecs.trimHeightMm);
    const innerW = parseFloat(innerTextJobSpecs.trimWidthMm);
    const innerQty = parseInt(innerTextJobSpecs.quantity, 10);

    if (!isNaN(innerH) && innerH > 0 && !isNaN(innerW) && innerW > 0 && !isNaN(innerQty) && innerQty > 0) {
      if (!jobSpecs.trimHeightMm) {
        setJobSpec('trimHeightMm', innerTextJobSpecs.trimHeightMm);
        setJobSpec('trimHeightIn', mmToIn(innerTextJobSpecs.trimHeightMm));
      }
      if (!jobSpecs.trimWidthMm) {
        setJobSpec('trimWidthMm', innerTextJobSpecs.trimWidthMm);
        setJobSpec('trimWidthIn', mmToIn(innerTextJobSpecs.trimWidthMm));
      }
      if (!jobSpecs.quantity) {
        setJobSpec('quantity', innerTextJobSpecs.quantity);
      }
    }
  }, [innerTextJobSpecs, jobSpecs, setJobSpec]);


  return (
    <CardWrapper>
      <div className='card-header'>
        <h2 className='text-xl font-semibold' data-translate-key='endpapersSpecsTitle'>
          Endpaper Specifications
        </h2>
      </div>
      <div className='space-y-4'>
        <div>
          <div className='mb-1 grid grid-cols-2 gap-x-4'>
            <label htmlFor='trimHeightMm' className='form-label' data-translate-key='endpaperHeightLabel'>
              Endpaper Height (mm)
            </label>
            <label htmlFor='trimHeightIn' className='form-label' data-translate-key='endpaperHeightLabelIn'>
              Endpaper Height (in)
            </label>
          </div>
          <div className='flex items-center gap-3'>
            <div className='relative flex-1'>
              <input
                type='number'
                id='trimHeightMm'
                className='form-input pr-8'
                value={jobSpecs.trimHeightMm}
                onChange={(e) => handleInputChange(e, 'trimHeightMm')}
                step='0.1'
                placeholder='e.g., 225'
                data-placeholder-translate-key='placeholderEndpaperHeight'
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>mm</span>
            </div>
            <div className='conversion-icon'>
              <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth='2' d='M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4'></path>
              </svg>
            </div>
            <div className='relative flex-1'>
              <input
                type='number'
                id='trimHeightIn'
                className='form-input pr-8'
                value={jobSpecs.trimHeightIn}
                onChange={(e) => handleInputChange(e, 'trimHeightIn')}
                step='0.01'
                placeholder='e.g., 8.86'
                data-placeholder-translate-key='placeholderEndpaperHeightIn'
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>in</span>
            </div>
          </div>
        </div>
        <div>
          <div className='mb-1 grid grid-cols-2 gap-x-4'>
            <label htmlFor='trimWidthMm' className='form-label' data-translate-key='endpaperWidthLabel'>
              Endpaper Width (mm)
            </label>
            <label htmlFor='trimWidthIn' className='form-label' data-translate-key='endpaperWidthLabelIn'>
              Endpaper Width (in)
            </label>
          </div>
          <div className='flex items-center gap-3'>
            <div className='relative flex-1'>
              <input
                type='number'
                id='trimWidthMm'
                className='form-input pr-8'
                value={jobSpecs.trimWidthMm}
                onChange={(e) => handleInputChange(e, 'trimWidthMm')}
                step='0.1'
                placeholder='e.g., 150'
                data-placeholder-translate-key='placeholderEndpaperWidth'
                data-title-translate-key='coverDimNote' // Reusing note key
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>mm</span>
            </div>
            <div className='conversion-icon'>
              <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth='2' d='M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4'></path>
              </svg>
            </div>
            <div className='relative flex-1'>
              <input
                type='number'
                id='trimWidthIn'
                className='form-input pr-8'
                value={jobSpecs.trimWidthIn}
                onChange={(e) => handleInputChange(e, 'trimWidthIn')}
                step='0.01'
                placeholder='e.g., 5.91'
                data-placeholder-translate-key='placeholderEndpaperWidthIn'
                data-title-translate-key='coverDimNote' // Reusing note key
              />
              <span className='absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500'>in</span>
            </div>
          </div>
        </div>
        {/* Responsive grid for input groups */}
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
          <div className='md:col-span-1'> {/* Endpaper Type */}
            <label htmlFor='endpaperType' className='form-label' data-translate-key='endpaperTypeLabel'>
              Endpaper Type
            </label>
            <select
              id='endpaperType'
              className='form-select'
              value={jobSpecs.endpaperType}
              onChange={(e) => handleInputChange(e, 'endpaperType')}
            >
              <option value='single' data-translate-key='endpaperTypeSingle'>
                Single Leaf (4pp per book)
              </option>
              <option value='double' data-translate-key='endpaperTypeDouble'>
                Double Leaf (8pp per book)
              </option>
            </select>
          </div>
          <div className='md:col-span-1'> {/* Quantity */}
            <label htmlFor='quantity' className='form-label' data-translate-key='quantityLabel'>
              Quantity
            </label>
            <input
              type='number'
              id='quantity'
              className='form-input'
              value={jobSpecs.quantity}
              onChange={(e) => handleInputChange(e, 'quantity')}
              placeholder='e.g., 1000'
              data-placeholder-translate-key='placeholderQuantity'
            />
          </div>
          <div className='md:col-span-1'> {/* Spoilage */}
            <label htmlFor='spoilagePercent' className='form-label' data-translate-key='spoilageLabel'>
              Spoilage (%)
            </label>
            <input
              type='number'
              id='spoilagePercent'
              className='form-input'
              value={jobSpecs.spoilagePercent}
              onChange={(e) => handleInputChange(e, 'spoilagePercent')}
              step='0.1'
              placeholder='e.g., 10'
              data-placeholder-translate-key='placeholderSpoilage'
            />
          </div>
        </div>
      </div>
    </CardWrapper>
  );
};

export default EndpapersSpecificationsCard;
