'use client';

import React, { useState } from 'react';
import { useInnerTextStore } from '@/stores/innerTextStore';
import { useCoverStore } from '@/stores/coverStore';
import { useEndpapersStore } from '@/stores/endpapersStore';
import { calculateAllPaperOptions, PaperOptionData, CalculationResult } from '@/utils/calculationEngine';

// Placeholder for an SVG icon component if you decide to make one
const PlusIcon = () => (
  <svg xmlns='http://www.w3.org/2000/svg' className='mr-1 inline-block h-5 w-5' viewBox='0 0 20 20' fill='currentColor'>
    <path
      fillRule='evenodd'
      d='M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z'
      clipRule='evenodd'
    />
  </svg>
);

const CalculateIcon = () => (
  <svg xmlns='http://www.w3.org/2000/svg' className='mr-1 inline-block h-5 w-5' viewBox='0 0 20 20' fill='currentColor'>
    <path
      fillRule='evenodd'
      d='M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00-.076-1.822l-10-4z'
      clipRule='evenodd'
    />
  </svg>
);

const DeleteIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
    </svg>
);

const initialDefaultPaperOptions: PaperOptionData[] = [
  { id: 1, paperName: 'Halved 31 x 43" 对开', source: 'Pre-Cut', sheetH_input: 546.1, sheetW_input: 787.4, grainDirInput: 'Height', caliperMicrons: 100, costReam: 50, gsm: 80, costTonne: undefined },
  { id: 2, paperName: 'Halved 35 x 47" 对开', source: 'Pre-Cut', sheetH_input: 596.9, sheetW_input: 889.0, grainDirInput: 'Height', caliperMicrons: 100, costReam: 22.50, gsm: 80, costTonne: undefined },
  { id: 3, paperName: 'Quarter 35 x 47" 四开', source: 'Pre-Cut', sheetH_input: 444.5, sheetW_input: 596.9, grainDirInput: 'Width', caliperMicrons: 100, costReam: 11.25, gsm: 80, costTonne: undefined },
  { id: 4, paperName: 'Special 25 x 38"', source: 'Pre-Cut', sheetH_input: 635.0, sheetW_input: 965.2, grainDirInput: 'Width', caliperMicrons: 100, costReam: 40, gsm: 80, costTonne: undefined },
  { id: 5, paperName: 'Custom 25" Roll', source: 'Roll', sheetH_input: 0, sheetW_input: 635.0, grainDirInput: 'Height', caliperMicrons: 100, costReam: undefined, gsm: 80, costTonne: 1200 },
  { id: 6, paperName: 'Custom 31" Roll', source: 'Roll', sheetH_input: 0, sheetW_input: 787.4, grainDirInput: 'Height', caliperMicrons: 100, costReam: undefined, gsm: 80, costTonne: 1200 },
  { id: 7, paperName: 'Custom 35" Roll', source: 'Roll', sheetH_input: 0, sheetW_input: 889.0, grainDirInput: 'Height', caliperMicrons: 100, costReam: undefined, gsm: 80, costTonne: 1200 },
  { id: 8, paperName: 'Custom 38" Roll', source: 'Roll', sheetH_input: 0, sheetW_input: 965.2, grainDirInput: 'Height', caliperMicrons: 100, costReam: undefined, gsm: 80, costTonne: 1200 },
];

interface PaperOptionsTableProps {
  setCalculationResults: React.Dispatch<React.SetStateAction<CalculationResult[]>>;
  componentType?: 'innerText' | 'cover' | 'endpapers';
}

const PaperOptionsTable: React.FC<PaperOptionsTableProps> = ({
  setCalculationResults,
  componentType = 'innerText'
}) => {
  const innerTextStore = useInnerTextStore();
  const coverStore = useCoverStore();
  const endpapersStore = useEndpapersStore();

  const [paperOptions, setPaperOptions] = useState<PaperOptionData[]>(initialDefaultPaperOptions);
  const [nextRowId, setNextRowId] = useState(initialDefaultPaperOptions.length + 1);

  const handlePaperOptionChange = (index: number, field: keyof PaperOptionData, value: any) => {
    const updatedOptions = [...paperOptions];
    const optionToUpdate = { ...updatedOptions[index] };

    if (['sheetH_input', 'sheetW_input', 'caliperMicrons', 'costReam', 'gsm', 'costTonne'].includes(field)) {
      const numValue = parseFloat(value);
      (optionToUpdate as any)[field] = isNaN(numValue) ? (value === '' ? undefined : value) : numValue;
    } else {
      (optionToUpdate as any)[field] = value;
    }
    updatedOptions[index] = optionToUpdate;
    setPaperOptions(updatedOptions);
  };

  const addPaperOptionRow = () => {
    setPaperOptions([
      ...paperOptions,
      {
        id: nextRowId,
        paperName: `New Option ${nextRowId}`,
        source: 'Pre-Cut',
        sheetH_input: 0,
        sheetW_input: 0,
        grainDirInput: 'Height',
        caliperMicrons: 0,
        costReam: undefined,
        gsm: undefined,
        costTonne: undefined,
      },
    ]);
    setNextRowId(nextRowId + 1);
  };

  const deletePaperOptionRow = (idToDelete: string | number) => {
    if (paperOptions.length <= 1) {
      console.warn("Cannot delete the last paper option.");
      return;
    }
    setPaperOptions(paperOptions.filter(option => option.id !== idToDelete));
  };

  const handleCalculateCosts = () => {
    let jobSpecs, prodParams;

    // Get the correct store based on component type
    if (componentType === 'innerText') {
      jobSpecs = innerTextStore.jobSpecs;
      prodParams = innerTextStore.prodParams;
    } else if (componentType === 'cover') {
      jobSpecs = coverStore.jobSpecs;
      prodParams = coverStore.prodParams;
    } else if (componentType === 'endpapers') {
      jobSpecs = endpapersStore.jobSpecs;
      prodParams = endpapersStore.prodParams;
    } else {
      console.error('Unknown component type:', componentType);
      return;
    }

    const results: CalculationResult[] = calculateAllPaperOptions(
      jobSpecs,
      prodParams,
      paperOptions,
      componentType
    );
    setCalculationResults(results);
    console.log(`Calculation Results for ${componentType}:`, results);
  };

  return (
    <div className='card shadow-md'>
      <div className='card-header collapsible' id={`paper-options-header_${componentType}`}>
        <div>
          <h2 className='text-xl font-semibold' data-translate-key='paperOptionsTitle'>
            Paper Options
          </h2>
        </div>
      </div>
      <div className='card-content' id={`paper-options-content_${componentType}`}>
        <p className='dark-mode-text mb-3 text-sm text-neutral-600' data-translate-key='paperOptionsNote'>
          Enter full original sheet dimensions. Sheets {'>'} 720x1020mm will be rotated/trimmed.
        </p>
        <div className='table-container'>
          <table id={`paper-options-table_${componentType}`}>
            <thead>
              <tr>
                <th className='th-paper-name' data-translate-key='colPaperName'>Paper Name</th>
                <th className='th-source' data-translate-key='colSource'>Source</th>
                <th className='th-sheet-h' data-translate-key='colSheetH'>Sheet H (mm)</th>
                <th className='th-sheet-w' data-translate-key='colSheetW'>Sheet W (mm)</th>
                <th className='th-grain-dir' data-translate-key='colGrainDir'>Grain || To</th>
                <th className='th-caliper' data-translate-key='colCaliper'>Caliper (µm)</th>
                <th className='th-cost-ream' data-translate-key='colCostReam'>Cost/Ream ($)</th>
                <th className='th-gsm' data-translate-key='colGsm'>GSM (g/m²)</th>
                <th className='th-cost-tonne' data-translate-key='colCostTonne'>Cost/Ton ($)</th>
                <th className='th-actions' data-translate-key='colActions'>Actions</th>
              </tr>
            </thead>
            <tbody>
              {paperOptions.map((option, index) => (
                <tr data-row-id={option.id} key={option.id}>
                  <td className='td-paper-name'>
                    <input type='text' className='form-input paper-name' value={option.paperName} onChange={(e) => handlePaperOptionChange(index, 'paperName', e.target.value)} />
                  </td>
                  <td className='td-source'>
                    <select className='form-select source' value={option.source} onChange={(e) => handlePaperOptionChange(index, 'source', e.target.value as 'Pre-Cut' | 'Roll')}>
                      <option value='Pre-Cut' data-translate-key='sourcePreCut'>Pre-Cut</option>
                      <option value='Roll' data-translate-key='sourceRoll'>Roll</option>
                    </select>
                  </td>
                  <td className='td-sheet-h'>
                    <input type='number' step='0.1' className='form-input sheet-h' value={option.sheetH_input || ''} onChange={(e) => handlePaperOptionChange(index, 'sheetH_input', e.target.value)} placeholder={option.source === 'Roll' ? '≤ 720' : ''} />
                  </td>
                  <td className='td-sheet-w'>
                    <input type='number' step='0.1' className='form-input sheet-w' value={option.sheetW_input || ''} onChange={(e) => handlePaperOptionChange(index, 'sheetW_input', e.target.value)} placeholder={option.source === 'Roll' ? '≤ 1020' : ''}/>
                  </td>
                  <td className='td-grain-dir'>
                    <select className='form-select grain-dir' value={option.grainDirInput} onChange={(e) => handlePaperOptionChange(index, 'grainDirInput', e.target.value as 'Height' | 'Width')}>
                      <option value='Height' data-translate-key='grainHeight'>Height</option>
                      <option value='Width' data-translate-key='grainWidth'>Width</option>
                    </select>
                  </td>
                  <td className='td-caliper'>
                    <input type='number' step='1' className='form-input caliper-microns' value={option.caliperMicrons || ''} onChange={(e) => handlePaperOptionChange(index, 'caliperMicrons', e.target.value)} placeholder='µm' />
                  </td>
                  <td className='td-cost-ream'>
                    <input type='number' step='0.01' className='form-input cost-ream' value={option.costReam || ''} onChange={(e) => handlePaperOptionChange(index, 'costReam', e.target.value)} placeholder={option.source === 'Roll' ? '' : '$'} disabled={option.source === 'Roll'}/>
                  </td>
                  <td className='td-gsm'>
                    <input type='number' step='1' className='form-input gsm' value={option.gsm || ''} onChange={(e) => handlePaperOptionChange(index, 'gsm', e.target.value)} placeholder='g/m²'/>
                  </td>
                  <td className='td-cost-tonne'>
                    <input type='number' step='0.01' className='form-input cost-tonne' value={option.costTonne || ''} onChange={(e) => handlePaperOptionChange(index, 'costTonne', e.target.value)} placeholder={option.source === 'Pre-Cut' ? '' : '$'} disabled={option.source === 'Pre-Cut'}/>
                  </td>
                  <td className='td-actions'>
                    <button className='delete-row' data-translate-key='deleteRowTitle' title='Delete row' onClick={() => deletePaperOptionRow(option.id)}>
                      <DeleteIcon />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className='mt-4 flex items-center justify-between'>
          <button
            id={`add-paper-option_${componentType}`}
            className='btn btn-secondary add-paper-option-btn flex items-center gap-1 px-4 py-2 shadow transition-all hover:shadow-md'
            onClick={addPaperOptionRow}
          >
            <PlusIcon />
            <span data-translate-key='addOptionBtn'>Add Option</span>
          </button>
          <button
            id={`calculate-btn_${componentType}`}
            className='btn btn-primary calculate-btn flex items-center gap-1 px-5 py-2 shadow-md transition-all hover:shadow-lg'
            tabIndex={15}
            onClick={handleCalculateCosts}
          >
            <CalculateIcon />
            <span data-translate-key='calculateBtn'>Calculate Costs</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaperOptionsTable;
