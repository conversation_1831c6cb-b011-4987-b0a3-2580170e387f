'use client';

import React, { ReactNode, useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

interface Tab {
  id: string;
  label: string;
  translateKey: string;
  content: ReactNode;
  icon?: string;
}

interface ParametersTabsProps {
  tabs: Tab[];
  defaultActiveTab?: string;
}

/**
 * ParametersTabs component provides a tabbed interface for switching between different parameter sections
 */
const ParametersTabs: React.FC<ParametersTabsProps> = ({ 
  tabs, 
  defaultActiveTab = tabs[0]?.id 
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultActiveTab);
  const { t } = useLanguage();

  // Find the active tab content
  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <div className="parameters-tabs bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 shadow-md overflow-hidden">
      {/* Tab Navigation */}
      <div className="flex border-b border-neutral-200 dark:border-neutral-700 overflow-x-auto">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-3 text-sm font-medium whitespace-nowrap transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-neutral-800 ${
              activeTab === tab.id
                ? 'text-primary border-b-2 border-primary dark:text-primary-light dark:border-primary-light'
                : 'text-neutral-600 hover:text-primary hover:bg-neutral-50 dark:text-neutral-300 dark:hover:text-primary-light dark:hover:bg-neutral-700'
            }`}
            aria-selected={activeTab === tab.id}
            role="tab"
            data-translate-key={tab.translateKey}
          >
            {tab.icon && <span className="mr-2">{tab.icon}</span>}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTabContent}
      </div>
    </div>
  );
};

export default ParametersTabs;
