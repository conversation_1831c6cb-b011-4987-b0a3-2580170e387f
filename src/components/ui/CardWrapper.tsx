import React from 'react';

interface CardWrapperProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * CardWrapper component that provides consistent styling for card components
 * This ensures all cards have the same base styling and behavior
 *
 * Note: This component is now used within pre-styled containers in the main layout
 */
const CardWrapper: React.FC<CardWrapperProps> = ({ children, className = '' }) => {
  return (
    <div className={`bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4 h-full ${className}`}>
      {children}
    </div>
  );
};

export default CardWrapper;
