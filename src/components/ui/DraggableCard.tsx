'use client';

import React, { ReactNode, useState, useRef, useEffect } from 'react';

interface DraggableCardProps {
  id: string;
  children: ReactNode;
  className?: string;
  onDragEnd?: (id: string, newPosition: { x: number, y: number }) => void;
  initialPosition?: { x: number, y: number };
  isDraggable?: boolean;
}

/**
 * DraggableCard component that allows users to drag and reposition cards
 */
const DraggableCard: React.FC<DraggableCardProps> = ({
  id,
  children,
  className = '',
  onDragEnd,
  initialPosition = { x: 0, y: 0 },
  isDraggable = true
}) => {
  const [position, setPosition] = useState(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);

  // Handle drag start
  const handleDragStart = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDraggable) return;

    setIsDragging(true);

    // Calculate the offset from the mouse position to the card's top-left corner
    if (cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }

    // Prevent default behavior to avoid browser's default drag behavior
    e.preventDefault();
  };

  // Handle drag
  const handleDrag = (e: MouseEvent) => {
    if (!isDragging || !isDraggable) return;

    // Calculate new position based on mouse position and drag offset
    setPosition({
      x: e.clientX - dragOffset.x,
      y: e.clientY - dragOffset.y
    });

    e.preventDefault();
  };

  // Handle drag end
  const handleDragEnd = () => {
    if (!isDragging || !isDraggable) return;

    setIsDragging(false);

    // Notify parent component of the new position
    if (onDragEnd) {
      onDragEnd(id, position);
    }
  };

  // Add and remove event listeners for drag
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleDrag);
      window.addEventListener('mouseup', handleDragEnd);
    }

    return () => {
      window.removeEventListener('mousemove', handleDrag);
      window.removeEventListener('mouseup', handleDragEnd);
    };
  }, [isDragging, position]);

  return (
    <div
      ref={cardRef}
      className={`card-container ${isDragging ? 'dragging' : ''} ${className}`}
      style={{
        position: isDragging ? 'absolute' : 'static',
        zIndex: isDragging ? 1000 : 1,
        cursor: isDraggable ? 'grab' : 'default',
        left: isDragging ? `${position.x}px` : 'auto',
        top: isDragging ? `${position.y}px` : 'auto',
        // Only set width when dragging - let CSS Grid handle layout when not dragging
        ...(isDragging && {
          width: 'calc(33.333% - 1.33rem)', // Adjusted for 2rem gap (2rem / 3 columns = 0.67rem per side)
          maxWidth: 'calc(33.333% - 1.33rem)'
        }),
        transition: isDragging ? 'none' : 'box-shadow 0.2s ease, transform 0.2s ease',
        // Ensure proper grid behavior when not dragging
        ...(!isDragging && {
          width: '100%',
          maxWidth: '100%',
          gridColumn: 'auto',
          gridRow: 'auto',
          // Ensure proper containment within grid cell
          minWidth: 0,
          overflow: 'visible'
        }),
      }}
      onMouseDown={handleDragStart}
      data-draggable-id={id}
    >
      <div className={`${isDragging ? 'cursor-grabbing' : ''}`}>
        {children}
      </div>
      {isDraggable && (
        <div className="drag-handle absolute top-2 right-2 text-neutral-400 hover:text-neutral-600 dark:text-neutral-500 dark:hover:text-neutral-300">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="8" cy="6" r="2" />
            <circle cx="8" cy="12" r="2" />
            <circle cx="8" cy="18" r="2" />
            <circle cx="16" cy="6" r="2" />
            <circle cx="16" cy="12" r="2" />
            <circle cx="16" cy="18" r="2" />
          </svg>
        </div>
      )}
    </div>
  );
};

export default DraggableCard;
