'use client';

import React, { useState, useEffect } from 'react';
import DraggableCard from './DraggableCard';

export interface CardItem {
  id: string;
  content: React.ReactNode;
  position?: { x: number, y: number };
}

interface DraggableCardContainerProps {
  cards: CardItem[];
  onCardsReordered?: (newCards: CardItem[]) => void;
  className?: string;
  isDraggable?: boolean;
}

/**
 * Container component for managing draggable cards
 */
const DraggableCardContainer: React.FC<DraggableCardContainerProps> = ({
  cards,
  onCardsReordered,
  className = '',
  isDraggable = true
}) => {
  const [cardItems, setCardItems] = useState<CardItem[]>(cards);
  
  // Update cards when props change
  useEffect(() => {
    setCardItems(cards);
  }, [cards]);

  // Handle card drag end
  const handleCardDragEnd = (id: string, newPosition: { x: number, y: number }) => {
    const updatedCards = cardItems.map(card => 
      card.id === id ? { ...card, position: newPosition } : card
    );
    
    setCardItems(updatedCards);
    
    if (onCardsReordered) {
      onCardsReordered(updatedCards);
    }
  };

  return (
    <div className={`draggable-card-container relative ${className}`}>
      {cardItems.map((card) => (
        <DraggableCard
          key={card.id}
          id={card.id}
          initialPosition={card.position}
          onDragEnd={handleCardDragEnd}
          isDraggable={isDraggable}
          className="mb-6 lg:mb-0"
        >
          {card.content}
        </DraggableCard>
      ))}
    </div>
  );
};

export default DraggableCardContainer;
