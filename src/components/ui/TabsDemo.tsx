'use client';

import React, { useState } from 'react';
import Tabs from './Tabs';

export default function TabsDemo() {
  const [activeTab1, setActiveTab1] = useState('tab1');
  const [activeTab2, setActiveTab2] = useState('tab1');
  const [activeTab3, setActiveTab3] = useState('tab1');

  const tabs = [
    {
      id: 'tab1',
      label: 'Inner Text',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      id: 'tab2',
      label: 'Cover',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76" />
        </svg>
      )
    },
    {
      id: 'tab3',
      label: 'Endpapers',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
        </svg>
      )
    }
  ];

  return (
    <div className="p-6 space-y-8">
      <div>
        <h2 className="text-lg font-medium mb-4">Pills Variant</h2>
        <Tabs
          tabs={tabs}
          activeTab={activeTab1}
          onTabChange={setActiveTab1}
          variant="pills"
          size="md"
        />
        <div className="mt-4 p-4 bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700">
          {activeTab1 === 'tab1' && <div>Inner Text Content</div>}
          {activeTab1 === 'tab2' && <div>Cover Content</div>}
          {activeTab1 === 'tab3' && <div>Endpapers Content</div>}
        </div>
      </div>

      <div>
        <h2 className="text-lg font-medium mb-4">Underlined Variant</h2>
        <Tabs
          tabs={tabs}
          activeTab={activeTab2}
          onTabChange={setActiveTab2}
          variant="underlined"
          size="md"
        />
        <div className="mt-4 p-4 bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700">
          {activeTab2 === 'tab1' && <div>Inner Text Content</div>}
          {activeTab2 === 'tab2' && <div>Cover Content</div>}
          {activeTab2 === 'tab3' && <div>Endpapers Content</div>}
        </div>
      </div>

      <div>
        <h2 className="text-lg font-medium mb-4">Filled Variant</h2>
        <Tabs
          tabs={tabs}
          activeTab={activeTab3}
          onTabChange={setActiveTab3}
          variant="filled"
          size="md"
        />
        <div className="mt-4 p-4 bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700">
          {activeTab3 === 'tab1' && <div>Inner Text Content</div>}
          {activeTab3 === 'tab2' && <div>Cover Content</div>}
          {activeTab3 === 'tab3' && <div>Endpapers Content</div>}
        </div>
      </div>
    </div>
  );
}
