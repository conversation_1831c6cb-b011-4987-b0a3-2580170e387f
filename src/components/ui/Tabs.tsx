'use client';

import React, { useState, useEffect, useRef } from 'react';

interface TabProps {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

interface TabsProps {
  tabs: TabProps[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  variant?: 'filled' | 'underlined' | 'clean';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  className?: string;
}

export default function Tabs({
  tabs,
  activeTab,
  onTabChange,
  variant = 'filled',
  size = 'md',
  fullWidth = false,
  className = '',
}: TabsProps) {
  const [indicatorStyle, setIndicatorStyle] = useState({});
  const tabsRef = useRef<(HTMLButtonElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Update the indicator position when the active tab changes
  useEffect(() => {
    const updateIndicator = () => {
      const activeIndex = tabs.findIndex(tab => tab.id === activeTab);
      if (activeIndex >= 0 && tabsRef.current[activeIndex]) {
        const activeTabElement = tabsRef.current[activeIndex];
        if (activeTabElement) {
          if (variant === 'underlined' || variant === 'clean') {
            setIndicatorStyle({
              width: `${activeTabElement.offsetWidth}px`,
              transform: `translateX(${activeTabElement.offsetLeft}px)`,
            });
          } else if (variant === 'filled') {
            setIndicatorStyle({
              width: `${activeTabElement.offsetWidth}px`,
              height: `${activeTabElement.offsetHeight}px`,
              transform: `translateX(${activeTabElement.offsetLeft}px)`,
            });
          }
        }
      }
    };

    updateIndicator();
    // Add resize listener to update indicator on window resize
    window.addEventListener('resize', updateIndicator);
    return () => window.removeEventListener('resize', updateIndicator);
  }, [activeTab, tabs, variant]);

  // Size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-sm py-1.5 px-3';
      case 'lg':
        return 'text-lg py-4 px-6 font-semibold';
      case 'md':
      default:
        return 'text-base py-2 px-4';
    }
  };

  // Variant classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'underlined':
        return 'bg-transparent hover:bg-white/10 dark:hover:bg-neutral-800/50 transition-all duration-300';
      case 'clean':
        return 'bg-transparent hover:bg-white/10 dark:hover:bg-neutral-800/50 transition-all duration-300';
      case 'filled':
      default:
        return 'rounded-t-lg';
    }
  };

  // Container classes
  const getContainerClasses = () => {
    switch (variant) {
      case 'underlined':
        return 'border-b border-neutral-200 dark:border-neutral-700';
      case 'clean':
        return 'relative bg-transparent backdrop-blur-xl border-b border-white/20 dark:border-neutral-700/30';
      case 'filled':
      default:
        return 'rounded-lg bg-white dark:bg-neutral-800 shadow-sm';
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative flex ${fullWidth ? 'w-full' : 'inline-flex'} ${getContainerClasses()} ${variant === 'clean' ? 'clean-tabs' : ''} ${className}`}
    >
      {variant === 'filled' && (
        <div
          className="absolute z-0 bg-white dark:bg-neutral-700 rounded-lg shadow-sm transition-all duration-300 ease-in-out"
          style={indicatorStyle}
        />
      )}

      {tabs.map((tab, index) => (
        <button
          key={tab.id}
          ref={el => { tabsRef.current[index] = el; }}
          className={`
            relative z-10 flex items-center justify-center transition-all duration-300 ease-out group
            ${getSizeClasses()}
            ${getVariantClasses()}
            ${fullWidth ? 'flex-1' : ''}
            ${activeTab === tab.id
              ? variant === 'filled'
                ? 'text-primary font-semibold shadow-sm'
                : variant === 'clean'
                  ? 'text-primary font-bold premium-clean-active-tab'
                  : 'text-primary font-semibold'
              : variant === 'clean'
                ? 'text-neutral-600 dark:text-neutral-300 hover:text-primary font-medium premium-clean-inactive-tab'
                : 'text-neutral-600 dark:text-neutral-400 hover:text-primary font-medium'
            }
          `}
          onClick={() => onTabChange(tab.id)}
          role="tab"
          aria-selected={activeTab === tab.id}
          tabIndex={activeTab === tab.id ? 0 : -1}
        >
          {tab.icon && <span className="mr-3 h-5 w-5 flex-shrink-0 transition-all duration-300">{tab.icon}</span>}
          <span className="whitespace-nowrap">{tab.label}</span>
        </button>
      ))}

      {(variant === 'underlined' || variant === 'clean') && (
        <div
          className={`absolute bottom-0 transition-all duration-300 ease-in-out ${
            variant === 'clean'
              ? 'h-1 bg-gradient-to-r from-primary to-secondary rounded-full'
              : 'h-0.5 bg-primary'
          }`}
          style={indicatorStyle}
        />
      )}
    </div>
  );
}
