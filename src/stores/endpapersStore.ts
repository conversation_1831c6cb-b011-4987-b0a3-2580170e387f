import { create } from 'zustand';

export interface EndpapersJobSpecs {
  trimHeightMm: string;
  trimHeightIn: string;
  trimWidthMm: string;
  trimWidthIn: string;
  endpaperType: 'single' | 'double';
  quantity: string;
  spoilagePercent: string;
}

export interface EndpapersProdParams {
  bleedMm: string;
  gripperMm: string;
  colorBarMm: string;
  alignmentMode: 'Aligned' | 'Misaligned';
  // Note: Side Lip is not applicable for Endpapers as per logic summary
}

interface EndpapersState {
  jobSpecs: EndpapersJobSpecs;
  prodParams: EndpapersProdParams;
  setJobSpec: (field: keyof EndpapersJobSpecs, value: string) => void;
  setProdParam: (field: keyof EndpapersProdParams, value: string) => void;
  toggleAlignmentMode: () => void;
  // TODO: Add actions for unit conversions if they modify these states directly
}

const initialEndpapersJobSpecs: EndpapersJobSpecs = {
  trimHeightMm: '', // Can be auto-filled from Inner Text
  trimHeightIn: '',
  trimWidthMm: '', // Can be auto-filled from Inner Text
  trimWidthIn: '',
  endpaperType: 'single', // Default
  quantity: '', // Can be auto-filled from Inner Text
  spoilagePercent: '10', // Default
};

const initialEndpapersProdParams: EndpapersProdParams = {
  bleedMm: '3', // Default
  gripperMm: '12', // Default
  colorBarMm: '6', // Default
  alignmentMode: 'Aligned', // Default to Aligned as per original HTML structure note
};

export const useEndpapersStore = create<EndpapersState>((set) => ({
  jobSpecs: initialEndpapersJobSpecs,
  prodParams: initialEndpapersProdParams,
  setJobSpec: (field, value) =>
    set((state) => ({
      jobSpecs: { ...state.jobSpecs, [field]: value },
    })),
  setProdParam: (field, value) =>
    set((state) => ({
      prodParams: { ...state.prodParams, [field]: value },
    })),
  toggleAlignmentMode: () =>
    set((state) => ({
      prodParams: {
        ...state.prodParams,
        alignmentMode:
          state.prodParams.alignmentMode === 'Aligned' ? 'Misaligned' : 'Aligned',
      },
    })),
}));

// TODO: Add initialization logic for inch values
