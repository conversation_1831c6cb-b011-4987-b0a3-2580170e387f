import { create } from 'zustand';

export interface InnerTextJobSpecs {
  trimHeightMm: string;
  trimHeightIn: string;
  trimWidthMm: string;
  trimWidthIn: string;
  totalPages: string;
  quantity: string;
  bindingMethod: string;
  spoilagePercent: string;
}

export interface InnerTextProdParams {
  bleedMm: string;
  gripperMm: string;
  colorBarMm: string;
  lipMm: string;
  isDoubleLipActive: boolean;
  alignmentMode: 'Aligned' | 'Misaligned';
}

interface InnerTextState {
  jobSpecs: InnerTextJobSpecs;
  prodParams: InnerTextProdParams;
  setJobSpec: (field: keyof InnerTextJobSpecs, value: string) => void;
  setProdParam: (
    field: keyof InnerTextProdParams,
    value: string | boolean
  ) => void;
  toggleDoubleLip: () => void;
  toggleAlignmentMode: () => void;
  // TODO: Add actions for unit conversions if they modify these states directly
}

const initialJobSpecs: InnerTextJobSpecs = {
  trimHeightMm: '225',
  trimHeightIn: '', // Will be calculated
  trimWidthMm: '150',
  trimWidthIn: '', // Will be calculated
  totalPages: '320',
  quantity: '1000',
  bindingMethod: 'saddleStitch',
  spoilagePercent: '5',
};

const initialProdParams: InnerTextProdParams = {
  bleedMm: '3',
  gripperMm: '12',
  colorBarMm: '6',
  lipMm: '5',
  isDoubleLipActive: false,
  alignmentMode: 'Misaligned', // Default to Misaligned as per original HTML structure
};

export const useInnerTextStore = create<InnerTextState>((set) => ({
  jobSpecs: initialJobSpecs,
  prodParams: initialProdParams,
  setJobSpec: (field, value) =>
    set((state) => ({
      jobSpecs: { ...state.jobSpecs, [field]: value },
    })),
  setProdParam: (field, value) =>
    set((state) => ({
      prodParams: { ...state.prodParams, [field]: value },
    })),
  toggleDoubleLip: () =>
    set((state) => ({
      prodParams: {
        ...state.prodParams,
        isDoubleLipActive: !state.prodParams.isDoubleLipActive,
      },
    })),
  toggleAlignmentMode: () =>
    set((state) => ({
      prodParams: {
        ...state.prodParams,
        alignmentMode:
          state.prodParams.alignmentMode === 'Aligned' ? 'Misaligned' : 'Aligned',
      },
    })),
}));

// Initialize inch values based on mm after store creation
const mmToIn = (mm: number): string => (mm / 25.4).toFixed(2); // Increased precision to 2 for inches

const initialTrimHIn = mmToIn(parseFloat(initialJobSpecs.trimHeightMm));
const initialTrimWIn = mmToIn(parseFloat(initialJobSpecs.trimWidthMm));

useInnerTextStore.setState((state) => ({
  jobSpecs: {
    ...state.jobSpecs,
    trimHeightIn: initialTrimHIn,
    trimWidthIn: initialTrimWIn,
  },
}));
