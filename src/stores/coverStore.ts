import { create } from 'zustand';

export interface CoverJobSpecs {
  trimHeightMm: string;
  trimHeightIn: string;
  trimWidthMm: string;
  trimWidthIn: string;
  spineThicknessMm: string;
  spineThicknessIn: string;
  coverType: 'paperback' | 'hardcover' | 'dustJacket';
  quantity: string;
  spoilagePercent: string;
  turnInAllowanceMm: string; // Only for hardcover
  flapWidthMm: string; // Only for dustJacket
}

export interface CoverProdParams {
  bleedMm: string;
  gripperMm: string;
  colorBarMm: string;
  alignmentMode: 'Aligned' | 'Misaligned';
  // Note: Side Lip is not applicable for Cover as per logic summary
}

interface CoverState {
  jobSpecs: CoverJobSpecs;
  prodParams: CoverProdParams;
  setJobSpec: (field: keyof CoverJobSpecs, value: string) => void;
  setProdParam: (field: keyof CoverProdParams, value: string) => void;
  toggleAlignmentMode: () => void;
  // TODO: Add actions for unit conversions if they modify these states directly
}

const initialCoverJobSpecs: CoverJobSpecs = {
  trimHeightMm: '', // Can be auto-filled from Inner Text
  trimHeightIn: '',
  trimWidthMm: '', // Can be auto-filled from Inner Text
  trimWidthIn: '',
  spineThicknessMm: '', // Can be auto-filled from Inner Text
  spineThicknessIn: '',
  coverType: 'paperback', // Default
  quantity: '', // Can be auto-filled from Inner Text
  spoilagePercent: '7', // Default
  turnInAllowanceMm: '18', // Default for hardcover
  flapWidthMm: '', // Can be auto-calculated
};

const initialCoverProdParams: CoverProdParams = {
  bleedMm: '5', // Default
  gripperMm: '12', // Default
  colorBarMm: '6', // Default
  alignmentMode: 'Aligned', // Default to Aligned as per original HTML structure note
};

export const useCoverStore = create<CoverState>((set) => ({
  jobSpecs: initialCoverJobSpecs,
  prodParams: initialCoverProdParams,
  setJobSpec: (field, value) =>
    set((state) => ({
      jobSpecs: { ...state.jobSpecs, [field]: value },
    })),
  setProdParam: (field, value) =>
    set((state) => ({
      prodParams: { ...state.prodParams, [field]: value },
    })),
  toggleAlignmentMode: () =>
    set((state) => ({
      prodParams: {
        ...state.prodParams,
        alignmentMode:
          state.prodParams.alignmentMode === 'Aligned' ? 'Misaligned' : 'Aligned',
      },
    })),
}));

// TODO: Add initialization logic for inch values and auto-calculation for flap width
