'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'zh';

interface LanguageContextType {
  language: Language;
  toggleLanguage: () => void;
  t: (key: string, fallback: string) => string;
}

// Comprehensive translations
const translations: Record<Language, Record<string, string>> = {
  en: {
    // App Title and Navigation
    appTitle: 'Multi-Component Paper Cost Estimator',
    appSubtitle: 'Estimate costs for Inner Text, Cover, and Endpapers. Max press: 720mm H x 1020mm W.',
    tabInnerText: 'Inner Text',
    tabCover: 'Cover',
    tabEndpapers: 'Endpapers',
    innerText: 'Inner Text',
    cover: 'Cover',
    endpapers: 'Endpapers',

    // Card Titles
    jobSpecsTitle: 'Job Specifications',
    prodParamsTitle: 'Production Parameters',
    coverSpecsTitle: 'Cover Specifications',
    coverProdParamsTitle: 'Cover Prod. Parameters',
    endpapersSpecsTitle: 'Endpaper Specifications',
    endpaperProdParamsTitle: 'Endpaper Prod. Parameters',
    converterTitle: 'Unit Converter',
    paperOptionsTitle: 'Paper Options',
    paperOptionsTitleCover: 'Cover Paper Options',
    paperOptionsTitleEndpapers: 'Endpaper Paper Options',
    resultsTitle: 'Calculation Results',
    resultsTitleCover: 'Cover Results',
    resultsTitleEndpapers: 'Endpaper Results',

    // Job Specifications
    pageHeightLabel: 'Page Height (mm)',
    pageHeightLabelIn: 'Page Height (in)',
    pageWidthLabel: 'Page Width (mm)',
    pageWidthLabelIn: 'Page Width (in)',
    totalPagesLabel: 'Total Pages',
    quantityLabel: 'Quantity',
    bindingLabel: 'Binding Method',
    spoilageLabel: 'Spoilage (%)',

    // Binding Methods
    bindingSaddleStitch: 'Saddle Stitch',
    bindingPerfectBound: 'Paperback',
    bindingWireO: 'Wire-O / Coil',
    bindingSectionSewn: 'Section Sewn',
    bindingCaseBound: 'Case Bound (Hardcover)',
    bindingSinglePage: 'Single Page / Flat Sheet',

    // Production Parameters
    bleedLabel: 'Bleed (mm)',
    gripperLabel: 'Gripper (mm)',
    colorBarLabel: 'Color Bar (mm)',
    gutterLabel: 'Gutter (mm)',
    lipLabel: 'Side Lip (mm)',
    doubleLipTitle: 'Double Side Lip',
    sideLipLabel: 'Side Lip',
    alignmentModeLabel: 'Grain Alignment',
    alignmentModeAligned: 'Aligned',
    alignmentModeMisaligned: 'Misaligned',
    prodParamsNote: '* Gutter not used for sheet area. Margins applied to 720H x 1020W press.',

    // Cover Specific
    coverHeightLabel: 'Cover Height (mm)',
    coverHeightLabelIn: 'Cover Height (in)',
    coverWidthLabel: 'Cover Width (mm)',
    coverWidthLabelIn: 'Cover Width (in)',
    coverDimNote: 'Typically same as Inner Text Page dimensions.',
    trimHeightLabel: 'Trim Height (mm)',
    trimWidthLabel: 'Trim Width (mm)',
    spineThicknessLabel: 'Spine Thickness (mm)',
    spineThicknessLabelIn: 'Spine Thickness (in)',
    coverTypeLabel: 'Cover Type',
    coverTypePaperback: 'Paperback',
    coverTypeHardcover: 'Hardcover (Case)',
    coverTypeDustJacket: 'Dust Jacket',
    coverQuantityNote: 'Matches Inner Text quantity.',
    coverGrainNote: 'Grain typically parallel to spine/cover height.',
    turnInAllowanceLabel: 'Turn-in Allowance (mm)',
    turnInAllowanceNote: 'Standard is 15-20mm for hardcover turn-ins.',
    flapWidthLabel: 'Flap Width (mm)',
    flapWidthNote: 'Typically half the book width. Auto-calculated by default.',

    // Endpapers Specific
    endpaperHeightLabel: 'Endpaper Height (mm)',
    endpaperHeightLabelIn: 'Endpaper Height (in)',
    endpaperWidthLabel: 'Endpaper Width (mm)',
    endpaperWidthLabelIn: 'Endpaper Width (in)',
    endpaperTypeLabel: 'Endpaper Type',
    endpaperTypeSingle: 'Single Leaf (4pp per book)',
    endpaperTypeDouble: 'Double Leaf (8pp per book)',

    // Unit Converter
    converterWeightTypeLabel: 'Paper Weight Type',
    converterWeightTypeText: 'Text',
    converterWeightTypeCover: 'Cover',
    converterLbsLabel: 'Pounds (lbs)',
    converterGsmLabel: 'GSM (g/m²)',
    converterThicknessLabel: 'Thickness',
    converterPtLabel: 'Points (pt)',
    converterMicronsLabel: 'Microns (µm)',
    converterInLabel: 'Inches (in)',
    converterMmLabel: 'Millimeters (mm)',
    mmToInLabel: 'mm to inches',
    inToMmLabel: 'inches to mm',
    gsm2BasisLabel: 'GSM to Basis Weight',
    basis2GsmLabel: 'Basis Weight to GSM',
    convertButton: 'Convert',

    // Paper Options
    paperOptionsNote: 'Enter full original sheet dimensions. Sheets > 720x1020mm will be rotated/trimmed.',
    addPaperButton: 'Add Paper',
    addOptionBtn: 'Add Option',
    paperNameLabel: 'Paper Name',
    sourceLabel: 'Source',
    sheetHeightLabel: 'Sheet Height (mm)',
    sheetWidthLabel: 'Sheet Width (mm)',
    grainDirLabel: 'Grain Direction',
    gsmLabel: 'GSM',
    caliperLabel: 'Caliper (µm)',
    costReamLabel: 'Cost per Ream',
    costTonneLabel: 'Cost per Tonne',
    calculateButton: 'Calculate',
    calculateBtnCover: 'Calculate Cover Costs',
    calculateBtnEndpapers: 'Calculate Endpaper Costs',

    // Table Headers
    colPaperName: 'Paper Name',
    colSource: 'Source',
    colSheetH: 'Sheet H (mm)',
    colSheetW: 'Sheet W (mm)',
    colGrainDir: 'Grain || To',
    colCaliper: 'Caliper (µm)',
    colCostReam: 'Cost/Ream ($)',
    colGsm: 'GSM (g/m²)',
    colCostTonne: 'Cost/Ton ($)',
    colActions: 'Actions',

    // Source Options
    sourcePreCut: 'Pre-Cut',
    sourceRoll: 'Roll',
    grainHeight: 'Height',
    grainWidth: 'Width',

    // Results
    mostEfficientBadge: 'Lowest Waste %',
    resInputSheet: 'Input Sheet (mm)',
    resPressSize: 'Press Size (mm)',
    resNoteRotated: '(Rotated for Press)',
    resNoteTrimmed: '(Trimmed from {h}H x {w}W)',
    resUsableArea: 'Usable Area (mm)',
    resGrainAlignment: 'Grain Alignment',
    grainAligned: 'Aligned',
    grainMisaligned: 'Misaligned',
    resUntrimmedPage: 'Untrimmed Page (mm)',
    resImposedArea: 'Imposed Area (mm)',
    resLayoutFit: 'Layout Fit (Down x Across)',
    resPagesPerSide: 'Pages/Side',
    resItemsPerSide: 'Covers/Side',
    resLeavesPerSide: 'Leaves/Side',
    resSheetUtilization: 'Sheet Utilization %',
    resResultingSig: 'Resulting Sig.',
    resTotalSheets: 'Total Sheets',
    resCostPerSheet: 'Cost/Sheet',
    resTotalCost: 'Total Cost',
    resBookBlockThickness: 'Book Block Thickness',
    resCostPerBook: 'Cost Per Book',
    resCostPerCover: 'Cost Per Cover',
    resCostPerEndpaperSet: 'Cost Per Endpaper Set',
    resErrorPrefix: 'Error',
    resSource: 'Source',
    resInputGrain: 'Input Grain',
    resGsm: 'GSM',

    // Error Messages
    errorInvalidSheetDims: 'Invalid sheet dimensions.',
    errorMissingCostReam: 'Missing Cost/Ream for Pre-Cut.',
    errorMissingRollData: 'Missing GSM, Cost/Tonne, Height or Width for Roll cost calculation.',
    errorSheetDimsAfterTrim: 'Sheet dimensions invalid after trimming.',
    errorUsableAreaNegative: 'Usable area zero/negative {note}',
    errorPageDimsInvalid: 'Page dimensions invalid.',
    errorFitCalc: 'Fit calculation error.',
    errorCannotFit: 'Cannot fit pages/items.',
    errorInvalidRollCost: 'Invalid Roll cost data.',
    errorCostSheetNegative: 'Cost/Sheet is zero/negative.',
    errorCostCalc: 'Final cost calculation error.',
    errorMissingCaliper: 'Missing Caliper (µm) for thickness calculation.',
    errorCoverSpreadSize: 'Invalid cover spread size calculation.',
    errorEndpaperSpreadSize: 'Invalid endpaper spread size calculation.',

    // Summary Panel
    summaryTitle: 'Selected Components',
    summaryNoSelection: 'No components selected yet.',
    summaryTotalCostPerBook: 'Total Cost/Book:',
    summaryInnerText: 'Inner Text',
    summaryCover: 'Cover',
    summaryEndpapers: 'Endpapers',
    summaryPaperDetails: 'Paper Details',
    summaryJobSpecs: 'Job Specs',
    summarySource: 'Source',
    summaryGrain: 'Grain',
    summaryGsm: 'GSM',
    summaryCaliper: 'Caliper',
    summaryPageSize: 'Size',
    summaryPageCount: 'Page Count',
    summaryQuantity: 'Quantity',
    summaryCoverType: 'Cover Type',
    summaryEndpaperType: 'Endpaper Type',
    selectThisOption: 'Select this Option',
    optionSelected: 'Selected',
    removeItemTitle: 'Remove item',

    // Alerts and Messages
    newOptionDefaultName: 'New Option {num}',
    alertLeastOneOption: 'You must keep at least one paper option.',
    alertInvalidInputs: 'Please enter valid positive values for required dimensions, pages, and quantity.',
    alertTotalPagesPositive: 'Total pages must be a positive number.',
    alertTotalPagesEven: 'Total pages must be an even number.',
    alertNegativeProdParams: 'Production parameters (Bleed, Gripper, Color Bar, Side Lip, Caliper, Costs, GSM) cannot be negative.',
    alertNoResults: 'No valid paper options entered or calculated. Please check inputs.',
    alertNoResultsForAlignment: 'No papers meet the {mode} grain requirement. Try changing the grain alignment.',
    deleteRowTitle: 'Delete row',
    modalCloseBtn: 'OK',
    na: 'N/A',

    // Placeholders
    placeholderPageHeight: 'e.g., 225',
    placeholderPageHeightIn: 'e.g., 8.86',
    placeholderPageWidth: 'e.g., 150',
    placeholderPageWidthIn: 'e.g., 5.91',
    placeholderTotalPages: 'e.g., 320',
    placeholderQuantity: 'e.g., 1000',
    placeholderSpoilage: 'e.g., 5',
    placeholderBleed: 'e.g., 3',
    placeholderGripper: 'e.g., 12',
    placeholderColorBar: 'e.g., 6',
    placeholderGutter: 'e.g., 6',
    placeholderLip: 'e.g., 5',
    placeholderCoverHeight: 'e.g., 225',
    placeholderCoverHeightIn: 'e.g., 8.86',
    placeholderCoverWidth: 'e.g., 150',
    placeholderCoverWidthIn: 'e.g., 5.91',
    placeholderSpineThickness: 'e.g., 15 or auto',
    placeholderSpineThicknessIn: 'e.g., 0.59 or auto',
    placeholderTurnInAllowance: 'e.g., 18',
    placeholderFlapWidth: 'e.g., half book width',
    placeholderEndpaperHeight: 'e.g., 225',
    placeholderEndpaperHeightIn: 'e.g., 8.86',
    placeholderEndpaperWidth: 'e.g., 150',
    placeholderEndpaperWidthIn: 'e.g., 5.91',
    placeholderRollH: '≤ 720',
    placeholderRollW: '≤ 1020',
    placeholderMm: 'mm',
    placeholderIn: 'in',
  },
  zh: {
    // App Title and Navigation
    appTitle: '多组件纸张成本估算器',
    appSubtitle: '估算内文、封面和环衬的成本。最大印刷机：720mm 高 x 1020mm 宽。',
    tabInnerText: '内文',
    tabCover: '封面',
    tabEndpapers: '环衬',
    innerText: '内文',
    cover: '封面',
    endpapers: '环衬',

    // Card Titles
    jobSpecsTitle: '工作规格',
    prodParamsTitle: '生产参数',
    coverSpecsTitle: '封面规格',
    coverProdParamsTitle: '封面生产参数',
    endpapersSpecsTitle: '环衬规格',
    endpaperProdParamsTitle: '环衬生产参数',
    converterTitle: '单位转换器',
    paperOptionsTitle: '纸张选项',
    paperOptionsTitleCover: '封面纸张选项',
    paperOptionsTitleEndpapers: '环衬纸张选项',
    resultsTitle: '计算结果',
    resultsTitleCover: '封面结果',
    resultsTitleEndpapers: '环衬结果',

    // Job Specifications
    pageHeightLabel: '页面高度 (mm)',
    pageHeightLabelIn: '页面高度 (in)',
    pageWidthLabel: '页面宽度 (mm)',
    pageWidthLabelIn: '页面宽度 (in)',
    totalPagesLabel: '总页数',
    quantityLabel: '数量',
    bindingLabel: '装订方式',
    spoilageLabel: '损耗 (%)',

    // Binding Methods
    bindingSaddleStitch: '骑马钉',
    bindingPerfectBound: '平装',
    bindingWireO: '铁圈装 / 螺旋装',
    bindingSectionSewn: '锁线装',
    bindingCaseBound: '精装（硬壳）',
    bindingSinglePage: '单页 / 平张',

    // Production Parameters
    bleedLabel: '出血 (mm)',
    gripperLabel: '咬口 (mm)',
    colorBarLabel: '色标 (mm)',
    gutterLabel: '装订边 (mm)',
    lipLabel: '侧边余量 (mm)',
    doubleLipTitle: '双侧边余量',
    sideLipLabel: '侧边余量',
    alignmentModeLabel: '纸纹对齐',
    alignmentModeAligned: '对齐',
    alignmentModeMisaligned: '不对齐',
    prodParamsNote: '* 装订边不用于纸张面积。边距应用于 720H x 1020W 印刷机。',

    // Cover Specific
    coverHeightLabel: '封面高度 (mm)',
    coverHeightLabelIn: '封面高度 (in)',
    coverWidthLabel: '封面宽度 (mm)',
    coverWidthLabelIn: '封面宽度 (in)',
    coverDimNote: '通常与内文页面尺寸相同。',
    trimHeightLabel: '成品高度 (mm)',
    trimWidthLabel: '成品宽度 (mm)',
    spineThicknessLabel: '书脊厚度 (mm)',
    spineThicknessLabelIn: '书脊厚度 (in)',
    coverTypeLabel: '封面类型',
    coverTypePaperback: '平装',
    coverTypeHardcover: '精装（硬壳）',
    coverTypeDustJacket: '护封',
    coverQuantityNote: '与内文数量匹配。',
    coverGrainNote: '纸纹通常平行于书脊/封面高度。',
    turnInAllowanceLabel: '包边余量 (mm)',
    turnInAllowanceNote: '精装包边标准为 15-20mm。',
    flapWidthLabel: '勒口宽度 (mm)',
    flapWidthNote: '通常为书宽的一半。默认自动计算。',

    // Endpapers Specific
    endpaperHeightLabel: '环衬高度 (mm)',
    endpaperHeightLabelIn: '环衬高度 (in)',
    endpaperWidthLabel: '环衬宽度 (mm)',
    endpaperWidthLabelIn: '环衬宽度 (in)',
    endpaperTypeLabel: '环衬类型',
    endpaperTypeSingle: '单叶（每本书 4 页）',
    endpaperTypeDouble: '双叶（每本书 8 页）',

    // Unit Converter
    converterWeightTypeLabel: '纸张重量类型',
    converterWeightTypeText: '内文纸',
    converterWeightTypeCover: '封面纸',
    converterLbsLabel: '磅 (lbs)',
    converterGsmLabel: 'GSM (g/m²)',
    converterThicknessLabel: '厚度',
    converterPtLabel: '点 (pt)',
    converterMicronsLabel: '微米 (µm)',
    converterInLabel: '英寸 (in)',
    converterMmLabel: '毫米 (mm)',
    mmToInLabel: 'mm 转 英寸',
    inToMmLabel: '英寸 转 mm',
    gsm2BasisLabel: 'GSM 转 基重',
    basis2GsmLabel: '基重 转 GSM',
    convertButton: '转换',

    // Paper Options
    paperOptionsNote: '输入完整原始纸张尺寸。大于 720x1020mm 的纸张将被旋转/裁切。',
    addPaperButton: '添加纸张',
    addOptionBtn: '添加选项',
    paperNameLabel: '纸张名称',
    sourceLabel: '来源',
    sheetHeightLabel: '纸张高度 (mm)',
    sheetWidthLabel: '纸张宽度 (mm)',
    grainDirLabel: '纸纹方向',
    gsmLabel: 'GSM',
    caliperLabel: '厚度 (µm)',
    costReamLabel: '每令成本',
    costTonneLabel: '每吨成本',
    calculateButton: '计算',
    calculateBtnCover: '计算封面成本',
    calculateBtnEndpapers: '计算环衬成本',

    // Table Headers
    colPaperName: '纸张名称',
    colSource: '来源',
    colSheetH: '纸张高 (mm)',
    colSheetW: '纸张宽 (mm)',
    colGrainDir: '纸纹 || 方向',
    colCaliper: '厚度 (µm)',
    colCostReam: '成本/令 ($)',
    colGsm: 'GSM (g/m²)',
    colCostTonne: '成本/吨 ($)',
    colActions: '操作',

    // Source Options
    sourcePreCut: '预切',
    sourceRoll: '卷筒',
    grainHeight: '高度',
    grainWidth: '宽度',

    // Results
    mostEfficientBadge: '最低损耗 %',
    resInputSheet: '输入纸张 (mm)',
    resPressSize: '印刷尺寸 (mm)',
    resNoteRotated: '（为印刷机旋转）',
    resNoteTrimmed: '（从 {h}H x {w}W 裁切）',
    resUsableArea: '可用面积 (mm)',
    resGrainAlignment: '纸纹对齐',
    grainAligned: '对齐',
    grainMisaligned: '不对齐',
    resUntrimmedPage: '未裁切页面 (mm)',
    resImposedArea: '拼版面积 (mm)',
    resLayoutFit: '版面适配（纵向 x 横向）',
    resPagesPerSide: '每面页数',
    resItemsPerSide: '每面封面数',
    resLeavesPerSide: '每面叶数',
    resSheetUtilization: '纸张利用率 %',
    resResultingSig: '结果签名',
    resTotalSheets: '总纸张数',
    resCostPerSheet: '每张成本',
    resTotalCost: '总成本',
    resBookBlockThickness: '书芯厚度',
    resCostPerBook: '每本成本',
    resCostPerCover: '每个封面成本',
    resCostPerEndpaperSet: '每套环衬成本',
    resErrorPrefix: '错误',
    resSource: '来源',
    resInputGrain: '输入纸纹',
    resGsm: 'GSM',

    // Error Messages
    errorInvalidSheetDims: '无效的纸张尺寸。',
    errorMissingCostReam: '预切纸张缺少每令成本。',
    errorMissingRollData: '卷筒纸成本计算缺少 GSM、每吨成本、高度或宽度。',
    errorSheetDimsAfterTrim: '裁切后纸张尺寸无效。',
    errorUsableAreaNegative: '可用面积为零/负数 {note}',
    errorPageDimsInvalid: '页面尺寸无效。',
    errorFitCalc: '适配计算错误。',
    errorCannotFit: '无法适配页面/项目。',
    errorInvalidRollCost: '无效的卷筒纸成本数据。',
    errorCostSheetNegative: '每张成本为零/负数。',
    errorCostCalc: '最终成本计算错误。',
    errorMissingCaliper: '厚度计算缺少厚度 (µm)。',
    errorCoverSpreadSize: '无效的封面展开尺寸计算。',
    errorEndpaperSpreadSize: '无效的环衬展开尺寸计算。',

    // Summary Panel
    summaryTitle: '已选组件',
    summaryNoSelection: '尚未选择组件。',
    summaryTotalCostPerBook: '每本总成本：',
    summaryInnerText: '内文',
    summaryCover: '封面',
    summaryEndpapers: '环衬',
    summaryPaperDetails: '纸张详情',
    summaryJobSpecs: '工作规格',
    summarySource: '来源',
    summaryGrain: '纸纹',
    summaryGsm: 'GSM',
    summaryCaliper: '厚度',
    summaryPageSize: '尺寸',
    summaryPageCount: '页数',
    summaryQuantity: '数量',
    summaryCoverType: '封面类型',
    summaryEndpaperType: '环衬类型',
    selectThisOption: '选择此选项',
    optionSelected: '已选择',
    removeItemTitle: '移除项目',

    // Alerts and Messages
    newOptionDefaultName: '新选项 {num}',
    alertLeastOneOption: '您必须保留至少一个纸张选项。',
    alertInvalidInputs: '请为所需的尺寸、页数和数量输入有效的正值。',
    alertTotalPagesPositive: '总页数必须是正数。',
    alertTotalPagesEven: '总页数必须是偶数。',
    alertNegativeProdParams: '生产参数（出血、咬口、色标、侧边余量、厚度、成本、GSM）不能为负数。',
    alertNoResults: '未输入或计算有效的纸张选项。请检查输入。',
    alertNoResultsForAlignment: '没有纸张满足 {mode} 纸纹要求。请尝试更改纸纹对齐。',
    deleteRowTitle: '删除行',
    modalCloseBtn: '确定',
    na: '不适用',

    // Placeholders
    placeholderPageHeight: '例如：225',
    placeholderPageHeightIn: '例如：8.86',
    placeholderPageWidth: '例如：150',
    placeholderPageWidthIn: '例如：5.91',
    placeholderTotalPages: '例如：320',
    placeholderQuantity: '例如：1000',
    placeholderSpoilage: '例如：5',
    placeholderBleed: '例如：3',
    placeholderGripper: '例如：12',
    placeholderColorBar: '例如：6',
    placeholderGutter: '例如：6',
    placeholderLip: '例如：5',
    placeholderCoverHeight: '例如：225',
    placeholderCoverHeightIn: '例如：8.86',
    placeholderCoverWidth: '例如：150',
    placeholderCoverWidthIn: '例如：5.91',
    placeholderSpineThickness: '例如：15 或 auto',
    placeholderSpineThicknessIn: '例如：0.59 或 auto',
    placeholderTurnInAllowance: '例如：18',
    placeholderFlapWidth: '例如：书宽的一半',
    placeholderEndpaperHeight: '例如：225',
    placeholderEndpaperHeightIn: '例如：8.86',
    placeholderEndpaperWidth: '例如：150',
    placeholderEndpaperWidthIn: '例如：5.91',
    placeholderRollH: '≤ 720',
    placeholderRollW: '≤ 1020',
    placeholderMm: 'mm',
    placeholderIn: 'in',
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Function to apply translations to all elements with data-translate-key
const applyTranslations = (lang: Language) => {
  if (typeof document === 'undefined') return; // Skip during SSR

  // Get all elements with data-translate-key attribute
  const elements = document.querySelectorAll('[data-translate-key]');

  elements.forEach(element => {
    const key = element.getAttribute('data-translate-key');
    if (!key) return;

    const translation = translations[lang][key];
    if (!translation) return;

    // Handle different element types
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      // For input placeholders
      if (element.hasAttribute('placeholder')) {
        const placeholderKey = element.getAttribute('data-placeholder-translate-key');
        if (placeholderKey && translations[lang][placeholderKey]) {
          (element as HTMLInputElement).placeholder = translations[lang][placeholderKey];
        }
      }
    } else if (element.tagName === 'OPTION') {
      // For select options
      element.textContent = translation;
    } else {
      // For regular elements
      element.textContent = translation;
    }
  });
};

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>('en');

  // Apply translations whenever language changes
  useEffect(() => {
    applyTranslations(language);
  }, [language]);

  useEffect(() => {
    // Check for saved language preference
    const savedLanguage = localStorage.getItem('language') as Language | null;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
      setLanguage(savedLanguage);

      // Update the UI to reflect the initial language
      setTimeout(() => {
        const langSwitch = document.querySelector('.premium-lang-switch');
        if (langSwitch) {
          if (savedLanguage === 'zh') {
            langSwitch.classList.add('active');
          } else {
            langSwitch.classList.remove('active');
          }
        }
      }, 0);
    }

    // Set up a mutation observer to apply translations to new elements
    const observer = new MutationObserver((mutations) => {
      let shouldApplyTranslations = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if any added nodes have data-translate-key
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.querySelector('[data-translate-key]') || element.hasAttribute('data-translate-key')) {
                shouldApplyTranslations = true;
              }
            }
          });
        }
      });

      if (shouldApplyTranslations) {
        applyTranslations(language);
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect();
  }, []);

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'zh' : 'en';
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);

    // Update the UI to reflect the language change
    const langSwitch = document.querySelector('.premium-lang-switch');
    if (langSwitch) {
      if (newLanguage === 'zh') {
        langSwitch.classList.add('active');
      } else {
        langSwitch.classList.remove('active');
      }
    }

    // Apply translations immediately
    applyTranslations(newLanguage);
  };

  const t = (key: string, fallback: string): string => {
    return translations[language][key] || fallback;
  };

  return (
    <LanguageContext.Provider value={{ language, toggleLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
