'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { CalculationResult } from '@/utils/calculationEngine';

interface SummaryItem {
  id: string | number;
  componentType: 'innerText' | 'cover' | 'endpapers';
  result: CalculationResult;
}

interface SummaryContextType {
  summaryItems: SummaryItem[];
  isPanelVisible: boolean;
  togglePanel: () => void;
  addToSummary: (item: SummaryItem) => void;
  removeFromSummary: (id: string | number) => void;
  clearSummary: () => void;
  totalCost: number;
}

const SummaryContext = createContext<SummaryContextType | undefined>(undefined);

export function SummaryProvider({ children }: { children: ReactNode }) {
  const [summaryItems, setSummaryItems] = useState<SummaryItem[]>([]);
  const [isPanelVisible, setIsPanelVisible] = useState(false);

  const togglePanel = () => {
    setIsPanelVisible(!isPanelVisible);
  };

  const addToSummary = (item: SummaryItem) => {
    // Check if item already exists, replace it if it does
    setSummaryItems(prev => {
      const exists = prev.some(i => i.id === item.id && i.componentType === item.componentType);
      if (exists) {
        return prev.map(i => 
          (i.id === item.id && i.componentType === item.componentType) ? item : i
        );
      } else {
        return [...prev, item];
      }
    });
  };

  const removeFromSummary = (id: string | number) => {
    setSummaryItems(prev => prev.filter(item => item.id !== id));
  };

  const clearSummary = () => {
    setSummaryItems([]);
  };

  // Calculate total cost
  const totalCost = summaryItems.reduce((sum, item) => {
    return sum + (item.result.costPerBook || 0);
  }, 0);

  return (
    <SummaryContext.Provider 
      value={{ 
        summaryItems, 
        isPanelVisible, 
        togglePanel, 
        addToSummary, 
        removeFromSummary, 
        clearSummary,
        totalCost
      }}
    >
      {children}
    </SummaryContext.Provider>
  );
}

export function useSummary() {
  const context = useContext(SummaryContext);
  if (context === undefined) {
    throw new Error('useSummary must be used within a SummaryProvider');
  }
  return context;
}
