'use client';

import React, { useState, useEffect } from 'react';
import JobSpecificationsCard from '@/components/features/JobSpecificationsCard';
import PaperOptionsTable from '@/components/features/PaperOptionsTable';
import ProductionParametersCard from '@/components/features/ProductionParametersCard';
import ResultsSection from '@/components/features/ResultsSection';
import UnitConverterCard from '@/components/features/UnitConverterCard';
import CoverSpecificationsCard from '@/components/features/CoverSpecificationsCard';
import CoverProductionParametersCard from '@/components/features/CoverProductionParametersCard';
import EndpapersSpecificationsCard from '@/components/features/EndpapersSpecificationsCard';
import EndpapersProductionParametersCard from '@/components/features/EndpapersProductionParametersCard';
import Sidebar from '@/components/layout/Sidebar';
import SummaryPanel from '@/components/features/SummaryPanel';
import DraggableCard from '@/components/ui/DraggableCard';
import { useSidebar } from '@/contexts/SidebarContext';
// import { useLanguage } from '@/contexts/LanguageContext'; // Removed unused import statement

import { CalculationResult } from '@/utils/calculationEngine';

type ComponentType = 'innerText' | 'cover' | 'endpapers';

interface CardPosition {
  [key: string]: { x: number, y: number };
}

export default function HomePage() {
  const [activeTab, setActiveTab] = useState<ComponentType>('innerText');
  const [calculationResults, setCalculationResults] = useState<CalculationResult[]>([]);
  const [cardPositions, setCardPositions] = useState<CardPosition>({});
  const { isCollapsed } = useSidebar();
  // const { t } = useLanguage(); // Removed unused variable

  // Load saved card positions from localStorage on initial render
  useEffect(() => {
    const savedPositions = localStorage.getItem('cardPositions');
    if (savedPositions) {
      try {
        setCardPositions(JSON.parse(savedPositions));
      } catch (e) {
        console.error('Failed to parse saved card positions:', e);
      }
    }
  }, []);

  const handleTabClick = (tab: ComponentType) => {
    setActiveTab(tab);
    setCalculationResults([]); // Clear results when switching tabs
  };

  // Handle card drag end and save positions
  const handleCardDragEnd = (id: string, newPosition: { x: number, y: number }) => {
    const updatedPositions = {
      ...cardPositions,
      [id]: newPosition
    };

    setCardPositions(updatedPositions);

    // Save to localStorage
    localStorage.setItem('cardPositions', JSON.stringify(updatedPositions));
  };

  // Get position for a card if it exists
  const getCardPosition = (id: string) => {
    return cardPositions[id] || { x: 0, y: 0 };
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 text-neutral-800 dark:from-neutral-900 dark:to-neutral-950 dark:text-neutral-100 transition-all duration-500'>
      <Sidebar activeTab={activeTab} onTabChange={handleTabClick} />
      <SummaryPanel />

      <main className={`main-content-with-sidebar ${isCollapsed ? 'sidebar-collapsed' : ''} mx-auto max-w-7xl py-8 premium-section`}>
        {/* Inner Text Tab */}
        {activeTab === 'innerText' && (
          <div className='animate-fadeIn'>
            {/* Premium Top Row - Three Cards Side by Side */}
            <div className='top-row-grid animate-slideUp'>
              {/* Job Specifications Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="innerText-jobSpecs"
                  initialPosition={getCardPosition("innerText-jobSpecs")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <JobSpecificationsCard />
                </DraggableCard>
              </div>

              {/* Production Parameters Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="innerText-prodParams"
                  initialPosition={getCardPosition("innerText-prodParams")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <ProductionParametersCard />
                </DraggableCard>
              </div>

              {/* Unit Converter Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="innerText-unitConverter"
                  initialPosition={getCardPosition("innerText-unitConverter")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <UnitConverterCard />
                </DraggableCard>
              </div>
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="innerText" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}

        {/* Cover Tab */}
        {activeTab === 'cover' && (
          <div className='animate-fadeIn'>
            {/* Premium Top Row - Three Cards Side by Side */}
            <div className='top-row-grid animate-slideUp'>
              {/* Cover Specifications Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="cover-specs"
                  initialPosition={getCardPosition("cover-specs")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <CoverSpecificationsCard />
                </DraggableCard>
              </div>

              {/* Cover Production Parameters Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="cover-prodParams"
                  initialPosition={getCardPosition("cover-prodParams")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <CoverProductionParametersCard />
                </DraggableCard>
              </div>

              {/* Unit Converter Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="cover-unitConverter"
                  initialPosition={getCardPosition("cover-unitConverter")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <UnitConverterCard />
                </DraggableCard>
              </div>
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="cover" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}

        {/* Endpapers Tab */}
        {activeTab === 'endpapers' && (
          <div className='animate-fadeIn'>
            {/* Premium Top Row - Three Cards Side by Side */}
            <div className='top-row-grid animate-slideUp'>
              {/* Endpapers Specifications Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="endpapers-specs"
                  initialPosition={getCardPosition("endpapers-specs")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <EndpapersSpecificationsCard />
                </DraggableCard>
              </div>

              {/* Endpapers Production Parameters Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="endpapers-prodParams"
                  initialPosition={getCardPosition("endpapers-prodParams")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <EndpapersProductionParametersCard />
                </DraggableCard>
              </div>

              {/* Unit Converter Card */}
              <div className="premium-card-container">
                <DraggableCard
                  id="endpapers-unitConverter"
                  initialPosition={getCardPosition("endpapers-unitConverter")}
                  onDragEnd={handleCardDragEnd}
                  className="w-full h-full"
                  isDraggable={false}
                >
                  <UnitConverterCard />
                </DraggableCard>
              </div>
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="endpapers" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
