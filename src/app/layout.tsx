import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { SummaryProvider } from '@/contexts/SummaryContext';
import { SidebarProvider } from '@/contexts/SidebarContext';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter', // Optional: if you want to use it as a CSS variable
});

export const metadata: Metadata = {
  title: 'Multi-Component Paper Cost Estimator',
  description: 'Estimate costs for Inner Text, Cover, and Endpapers.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en'>
      <body className={`${inter.className} antialiased`}>
        <ThemeProvider>
          <LanguageProvider>
            <SummaryProvider>
              <SidebarProvider>
                {children}
              </SidebarProvider>
            </SummaryProvider>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
