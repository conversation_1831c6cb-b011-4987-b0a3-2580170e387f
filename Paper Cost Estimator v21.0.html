<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate-key="appTitle">Multi-Component Paper Cost Estimator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Color Variables */
            --primary: #4f46e5; /* Indigo-600 */
            --primary-light: #818cf8; /* Indigo-400 */
            --primary-dark: #4338ca; /* Indigo-700 */
            --secondary: #0ea5e9; /* Sky-500 */
            --accent: #f59e0b; /* Amber-500 */
            --neutral-50: #f9fafb; /* Gray-50 */
            --neutral-100: #f3f4f6; /* Gray-100 */
            --neutral-200: #e5e7eb; /* Gray-200 */
            --neutral-300: #d1d5db; /* Gray-300 */
            --neutral-400: #9ca3af; /* Gray-400 */
            --neutral-500: #6b7280; /* Gray-500 */
            --neutral-600: #4b5563; /* Gray-600 */
            --neutral-700: #374151; /* Gray-700 */
            --neutral-800: #1f2937; /* Gray-800 */
            --neutral-900: #111827; /* Gray-900 */
            --success: #10b981; /* Emerald-500 */
            --danger: #ef4444; /* Red-500 */
            --info: #3b82f6; /* Blue-500 */

            /* Transition Variables - Standardized for consistency */
            --transition-duration: 0.3s;
            --transition-timing: ease;
            --transition-theme: background-color var(--transition-duration) var(--transition-timing),
                               color var(--transition-duration) var(--transition-timing),
                               border-color var(--transition-duration) var(--transition-timing),
                               box-shadow var(--transition-duration) var(--transition-timing);
            --transition-interactive: all 0.15s ease-in-out; /* For buttons and interactive elements */
            --transition-transform: transform 0.3s var(--transition-timing);
        }
        html { scroll-behavior: smooth; }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--neutral-100);
            color: var(--neutral-800);
            transition: var(--transition-theme);
        }
        header { transition: var(--transition-theme); }
        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: var(--transition-theme);
            display: flex;
            flex-direction: column;
        }
        .card-header {
            border-bottom: 1px solid var(--neutral-200);
            padding-bottom: 0.75rem;
            margin-bottom: 1rem;
            transition: var(--transition-theme);
        }
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
        input[type=number] { -moz-appearance: textfield; appearance: textfield; }
        .form-input {
            width: 100%; padding: 0.5rem 0.75rem; border: 1px solid var(--neutral-300);
            border-radius: 0.375rem; font-size: 0.875rem;
            transition: var(--transition-theme);
            box-sizing: border-box; background-color: white; color: var(--neutral-800);
        }
        .form-select {
            width: 100%; padding: 0.5rem 0.75rem; border: 1px solid var(--neutral-300);
            border-radius: 0.375rem; font-size: 0.875rem; box-sizing: border-box;
            font-family: inherit; line-height: inherit; color: inherit; background-color: white;
            transition: var(--transition-theme);
            -webkit-appearance: none; -moz-appearance: none; appearance: none;
            padding-right: 2.5rem;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");
            background-repeat: no-repeat; background-position: right 0.7rem center; background-size: 1.25em 1.25em;
        }
        .form-input::placeholder { color: var(--neutral-400); font-size: 0.8rem; transition: var(--transition-theme); }
        .form-input:focus, .form-select:focus {
            border-color: var(--primary); box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2); outline: none;
        }
        .input-error { border-color: var(--danger) !important; box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2); }
        .form-label {
            display: block; font-size: 0.875rem; font-weight: 500;
            color: var(--neutral-700); margin-bottom: 0.375rem; transition: var(--transition-theme);
        }
        .conversion-icon {
            flex-shrink: 0; color: var(--secondary); transition: var(--transition-theme), transform var(--transition-duration) var(--transition-timing);
            padding: 0.25rem; border-radius: 0.375rem; background-color: var(--neutral-100);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        .conversion-icon:hover { background-color: var(--neutral-200); transform: scale(1.05); }
        body.dark-mode .conversion-icon { color: var(--neutral-500); background-color: var(--neutral-800); }
        body.dark-mode .conversion-icon:hover { background-color: var(--neutral-700); }


        /* Custom scrollbar styles */
        ::-webkit-scrollbar { width: 10px; height: 10px; }
        ::-webkit-scrollbar-track { background: var(--neutral-200); }
        ::-webkit-scrollbar-thumb { background-color: var(--neutral-400); border-radius: 5px; }
        body.dark-mode { scrollbar-color: var(--neutral-600) var(--neutral-800); scrollbar-width: thin; }
        body.dark-mode ::-webkit-scrollbar-track { background: var(--neutral-800); }
        body.dark-mode ::-webkit-scrollbar-thumb { background-color: var(--neutral-600); }

        .table-container {
            border-radius: 0.375rem; border: 1px solid var(--neutral-200);
            transition: var(--transition-theme); flex-grow: 1; margin-bottom: 0.2rem;
            overflow-x: auto; -webkit-overflow-scrolling: touch;
        }
        table { width: 100%; border-collapse: collapse; font-size: 0.875rem; table-layout: fixed; }
        th {
            background-color: var(--neutral-50); font-weight: 600; text-align: left;
            padding: 0.75rem 1rem; border-bottom: 1px solid var(--neutral-200);
            white-space: nowrap;
            transition: var(--transition-theme);
        }
        td {
            padding: 0.5rem 1rem; border-bottom: 1px solid var(--neutral-200);
            vertical-align: middle; transition: var(--transition-theme);
        }
        td .form-input, td .form-select { max-width: 100%; }
        tr:last-child td { border-bottom: none; }

        th.th-paper-name, td.td-paper-name { width: 180px; }
        th.th-source, td.td-source { width: 120px; }
        th.th-sheet-h, td.td-sheet-h { width: 100px; }
        th.th-sheet-w, td.td-sheet-w { width: 100px; }
        th.th-grain-dir, td.td-grain-dir { width: 110px; }
        th.th-caliper, td.td-caliper { width: 90px; }
        th.th-cost-ream, td.td-cost-ream { width: 110px; }
        th.th-gsm, td.td-gsm { width: 90px; }
        th.th-cost-tonne, td.td-cost-tonne { width: 100px; }
        th.th-actions, td.td-actions { width: 80px; text-align: center; }

        .result-card {
            background-color: white; border-radius: 0.375rem; padding: 1rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); border: 1px solid var(--neutral-200);
            position: relative; padding-top: 1.5rem;
            transition: var(--transition-theme);
            min-height: 380px; display: flex; flex-direction: column;
        }
        .result-label { font-size: 0.75rem; color: var(--neutral-500); margin-bottom: 0.125rem; transition: var(--transition-theme); }
        .result-value {
            font-size: 0.9rem; font-weight: 600; color: var(--neutral-800);
            word-wrap: break-word; white-space: normal; transition: var(--transition-theme);
        }
        .result-value-small { font-size: 0.8rem; font-weight: 500; color: var(--neutral-600); transition: var(--transition-theme); }
        .result-note { font-size: 0.75rem; font-style: italic; color: var(--neutral-500); display: inline; margin-left: 0.25rem; transition: var(--transition-theme); }
        .grain-aligned { color: var(--success); }
        .grain-misaligned { color: var(--danger); }
        .best-option { border: 2px solid var(--accent); }
        .best-option-badge {
            position: absolute; top: 0; right: 0; background-color: var(--accent); color: white;
            font-size: 0.75rem; font-weight: 600; padding: 0.25rem 0.75rem;
            border-radius: 0 0.25rem 0 0.25rem; z-index: 5; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn {
            padding: 0.5rem 1rem; border-radius: 0.375rem; font-weight: 500; cursor: pointer;
            transition: var(--transition-interactive); display: inline-flex; align-items: center;
            gap: 0.25rem; font-size: 0.875rem;
        }
        .btn-primary { background-color: var(--primary); color: white; border: 1px solid var(--primary-dark); }
        .btn-primary:hover { background-color: var(--primary-dark); }
        .btn-secondary { background-color: white; color: var(--neutral-700); border: 1px solid var(--neutral-300); }
        .btn-secondary:hover { background-color: var(--neutral-50); }
        .btn-success { background-color: var(--success); color: white; border: 1px solid #059669; } /* Emerald-600 for border */
        .btn-success:hover { background-color: #059669; }

        .delete-row {
            color: #ef4444;
            background: none;
            border: none;
            padding: 0.25rem;
            cursor: pointer;
            transition: var(--transition-interactive), background-color var(--transition-duration) var(--transition-timing);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 1.75rem;
            height: 1.75rem;
            border-radius: 0.25rem;
        }
        .delete-row:hover {
            color: #dc2626;
            background-color: rgba(239, 68, 68, 0.1);
        }
        .delete-row svg {
            width: 1.25rem;
            height: 1.25rem;
            pointer-events: none; /* Ensure clicks pass through to the button */
        }
        .delete-row svg path {
            pointer-events: none; /* Ensure clicks pass through to the button */
        }
        .btn svg { width: 1rem; height: 1rem; }

        .lang-switch, .theme-switch, .alignment-switch, .double-lip-switch { /* Common switch base */
            position: relative; display: inline-block; cursor: pointer;
            transition: var(--transition-theme), transform var(--transition-duration) var(--transition-timing);
            overflow: hidden;
            -webkit-tap-highlight-color: transparent; user-select: none; outline: none;
        }
        .lang-switch:before, .theme-switch:before, .alignment-switch:before, .double-lip-switch:before {
            content: ''; position: absolute; top: -10px; left: -10px; right: -10px; bottom: -10px;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
            opacity: 0; transition: opacity var(--transition-duration) var(--transition-timing); pointer-events: none;
        }
        .lang-switch:hover:before, .theme-switch:hover:before, .alignment-switch:hover:before, .double-lip-switch:hover:before { opacity: 1; }

        .lang-switch {
            background: #ef4444; padding: 0.25rem; border-radius: 9999px;
            width: 80px; height: 36px;
            box-shadow: 0 4px 10px rgba(79, 70, 229, 0.6), inset 0 2px 3px rgba(255, 255, 255, 0.2);
        }
        .lang-switch.active { background: #4f46e5; box-shadow: 0 4px 10px rgba(239, 68, 68, 0.6), inset 0 2px 3px rgba(255, 255, 255, 0.2); }
        .lang-switch:hover { transform: translateY(-1px); }
        .lang-switch-label {
            font-size: 0.75rem; font-weight: 600; z-index: 1; position: absolute;
            top: 50%; transform: translateY(-50%); transition: var(--transition-theme);
            color: rgba(255, 255, 255, 0.7); letter-spacing: 0.5px;
        }
        .lang-switch-label.en { left: 15px; } .lang-switch-label.zh { right: 15px; }
        .lang-switch-toggle {
            width: 30px; height: 30px; background: #ffffff; border-radius: 9999px;
            position: absolute; top: 3px; left: 3px;
            transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1); /* Keep the bouncy effect for toggle */
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); z-index: 2;
        }
        .lang-switch-toggle:after {
            content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 16px; height: 16px; background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 50%; opacity: 0.15; transition: opacity var(--transition-duration) var(--transition-timing);
        }
        .lang-switch.active .lang-switch-toggle { transform: translateX(44px); }
        .lang-switch.active .lang-switch-label.en { color: rgba(255, 255, 255, 0.9); }
        .lang-switch.active .lang-switch-label.zh { color: #ffffff; font-weight: 800; }
        .lang-switch:not(.active) .lang-switch-label.en { color: #ffffff; font-weight: 800; }
        .lang-switch:not(.active):active .lang-switch-toggle { transform: scale(0.95); }
        .lang-switch.active:active .lang-switch-toggle { transform: translateX(44px) scale(0.95); }
        .lang-switch:active .lang-switch-toggle:after { opacity: 0.3; }

        .theme-switch {
            background: linear-gradient(135deg, #f59e0b, #fbbf24); padding: 0.25rem; border-radius: 9999px;
            width: 80px; height: 36px;
            box-shadow: 0 4px 10px rgba(245, 158, 11, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
        }
        .theme-switch:hover {
            box-shadow: 0 6px 12px rgba(245, 158, 11, 0.4), inset 0 2px 3px rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        .theme-switch.dark { background: linear-gradient(135deg, #1f2937, #374151); }
        .theme-icon {
            position: absolute; top: 50%; transform: translateY(-50%); z-index: 1;
            transition: var(--transition-theme); color: rgba(255, 255, 255, 0.9); opacity: 1 !important;
        }
        .theme-icon.sun { width: 20px; height: 20px; left: 12px; }
        .theme-icon.moon { width: 16px; height: 16px; right: 12px; }
        .theme-switch-toggle {
            width: 30px; height: 30px; background: #ffffff; border-radius: 9999px;
            position: absolute; top: 3px; left: 3px;
            transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1); /* Keep the bouncy effect for toggle */
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); z-index: 2;
            display: flex; align-items: center; justify-content: center;
        }
        .theme-switch-toggle:after {
            content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 16px; height: 16px; background: linear-gradient(135deg, #f59e0b, #fbbf24);
            border-radius: 50%; opacity: 0.15; transition: opacity var(--transition-duration) var(--transition-timing), background var(--transition-duration) var(--transition-timing);
        }
        .theme-switch.dark .theme-switch-toggle { transform: translateX(44px); }
        .theme-switch.dark .theme-switch-toggle:after { background: linear-gradient(135deg, #1f2937, #374151); }
        .theme-switch:not(.dark):active .theme-switch-toggle { transform: scale(0.95); }
        .theme-switch.dark:active .theme-switch-toggle { transform: translateX(44px) scale(0.95); }

        /* Dark Mode Styles */
        body.dark-mode { background-color: var(--neutral-900); color: var(--neutral-100); }
        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 { color: var(--neutral-100); }
        body.dark-mode p, body.dark-mode .dark-mode-text { color: var(--neutral-300); }
        body.dark-mode header { background-color: var(--neutral-800); } /* Added rule for dark mode header */
        body.dark-mode .card { background-color: var(--neutral-800); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); }
        body.dark-mode .card-header { border-bottom-color: var(--neutral-700); }
        body.dark-mode .form-input, body.dark-mode .form-select {
            background-color: var(--neutral-700); border-color: var(--neutral-600); color: var(--neutral-100);
        }
        body.dark-mode .form-input::placeholder { color: var(--neutral-500); }
        body.dark-mode .form-input:focus, body.dark-mode .form-select:focus {
            border-color: var(--primary-light); box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
            background-color: var(--neutral-600);
        }
        body.dark-mode .form-select {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%239ca3af'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");
        }
        body.dark-mode .form-label { color: var(--neutral-300); }
        body.dark-mode .table-container { border-color: var(--neutral-700); }
        body.dark-mode th { background-color: var(--neutral-800); border-bottom-color: var(--neutral-700); color: var(--neutral-200); }
        body.dark-mode td { border-bottom-color: var(--neutral-700); color: var(--neutral-300); }
        body.dark-mode .result-card { background-color: var(--neutral-800); border-color: var(--neutral-700); box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); }
        body.dark-mode .result-label { color: var(--neutral-400); }
        body.dark-mode .result-value { color: var(--neutral-100); }
        body.dark-mode .result-value-small { color: var(--neutral-400); }
        body.dark-mode .result-note { color: var(--neutral-500); }
        body.dark-mode .btn-secondary { background-color: var(--neutral-700); color: var(--neutral-200); border-color: var(--neutral-600); }
        body.dark-mode .btn-secondary:hover { background-color: var(--neutral-600); }
        body.dark-mode .modal-content { background-color: var(--neutral-800); box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); }
        body.dark-mode .modal-message { color: var(--neutral-300); }
        body.dark-mode .delete-row { color: var(--danger); }
        body.dark-mode .delete-row:hover {
            color: #ff7f7f;
            background-color: rgba(239, 68, 68, 0.15);
        }
        body.dark-mode .converter-divider { border-color: var(--neutral-700); }

        .modal-overlay {
            position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.6);
            display: flex; align-items: center; justify-content: center; z-index: 1000;
            opacity: 0; visibility: hidden; transition: opacity var(--transition-duration) var(--transition-timing),
                                                       visibility var(--transition-duration) var(--transition-timing);
        }
        .modal-overlay.visible { opacity: 1; visibility: visible; }
        .modal-content {
            background-color: white; padding: 1.5rem 2rem; border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); max-width: 400px; text-align: center;
            transition: var(--transition-theme);
        }
        .modal-message { margin-bottom: 1rem; color: var(--neutral-700); transition: var(--transition-theme); }

        @keyframes textFadeOut { from { opacity: 1; transform: translateY(0); } to { opacity: 0; transform: translateY(-4px); } }
        @keyframes textFadeIn { from { opacity: 0; transform: translateY(4px); } to { opacity: 1; transform: translateY(0); } }
        .text-transition-out { animation: textFadeOut 0.12s ease-out forwards; }
        .text-transition-in { animation: textFadeIn 0.16s ease-in forwards; }
        [data-translate-key] { display: inline-block; vertical-align: bottom; }
        h1[data-translate-key], h2[data-translate-key], h3[data-translate-key], p[data-translate-key], label[data-translate-key], span[data-translate-key], button > span[data-translate-key] { display: inline-block; }
        header h1[data-translate-key="appTitle"], header p[data-translate-key="appSubtitle"] { display: block; } /* Adjusted for new keys */
        .card-header h2[data-translate-key] { display: block; }
        th[data-translate-key] { display: table-cell; }
        td[data-translate-key] > div { display: block; }
        label.form-label[data-translate-key] { display: block; }
        button[data-translate-key] > span { vertical-align: middle; }

        .card-header.collapsible {
            cursor: pointer; display: flex; justify-content: space-between; align-items: center;
            padding-right: 0.75rem; transition: var(--transition-theme);
        }
        .card-header.collapsible:hover { background-color: var(--neutral-100); }
        body.dark-mode .card-header.collapsible:hover { background-color: var(--neutral-700); }
        .card-content { overflow: hidden; transition: max-height var(--transition-duration) ease-out; }
        .card-content.collapsed { max-height: 0 !important; padding-top: 0; padding-bottom: 0; margin-bottom: 0; } /* Ensure full collapse */
        .toggle-icon { transition: var(--transition-transform); color: var(--neutral-500); }
        .toggle-icon.collapsed { transform: rotate(180deg); }

        .card-glow { box-shadow: 0 0 0 2px rgba(246, 156, 30, 0.2), 0 0 10px rgba(124, 58, 237, 0.3); transition: box-shadow 0.5s ease-out; }
        @keyframes pulse-glow {
            0% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
            50% { box-shadow: 0 0 0 2px rgba(241,173,79,0.3), 0 0 15px rgba(124,58,237,0.4); }
            100% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
        }
        .pulse-glow { animation: pulse-glow 1.6s ease-in-out infinite; border-radius: 0.5rem; }
        .fade-out-glow { animation: fade-out-pulse 1.5s forwards ease-out; border-radius: 0.5rem; }
        @keyframes fade-out-pulse {
            0% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
            100% { box-shadow: 0 0 0 0 rgba(246,156,30,0), 0 0 0 rgba(124,58,237,0); }
        }

        body.dark-mode .grain-aligned { color: var(--success); }
        body.dark-mode .grain-misaligned { color: var(--danger); }
        body.dark-mode .text-red-500, body.dark-mode .text-red-600 { color: #f87171; }
        body.dark-mode .text-danger { color: #f87171; }
        body.dark-mode .text-success { color: #34d399; }
        body.dark-mode .text-info { color: #60a5fa; }
        body.dark-mode .text-warning { color: #fbbf24; }

        .alignment-switch {
            width: 60px; height: 26px; border-radius: 13px;
            background: linear-gradient(135deg, #10b981, #34d399);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.3);
        }
        .alignment-switch-toggle {
            position: absolute; /* Added explicitly */
            top: 3px; left: 3px; width: 20px; height: 20px; border-radius: 50%;
            background: #ffffff; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); z-index: 2;
        }
        .alignment-switch-toggle:after {
            content: ""; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 10px; height: 10px; border-radius: 50%;
            background: rgba(16, 185, 129, 0.2); transition: all 0.3s ease;
        }
        .alignment-switch.misaligned { background: linear-gradient(135deg, #ef4444, #f87171); box-shadow: 0 2px 5px rgba(239, 68, 68, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.3); }
        .alignment-switch.misaligned .alignment-switch-toggle { transform: translateX(34px); }
        .alignment-switch.misaligned .alignment-switch-toggle:after { background: rgba(239, 68, 68, 0.2); }
        .alignment-switch:not(.misaligned):active .alignment-switch-toggle { transform: scale(0.95); }
        .alignment-switch.misaligned:active .alignment-switch-toggle { transform: translateX(34px) scale(0.95); }
        .alignment-mode-text { font-size: 0.875rem; transition: all 0.3s ease; margin-left: 0.5rem; }
        .alignment-mode-text.aligned-text { display: none; font-weight: 600; color: #10b981; }
        .alignment-mode-text.misaligned-text { display: inline-block; font-weight: 600; color: #ef4444; }
        .alignment-switch:not(.misaligned) ~ span .alignment-mode-text.aligned-text { display: inline-block; }
        .alignment-switch:not(.misaligned) ~ span .alignment-mode-text.misaligned-text { display: none; }
        body.dark-mode .alignment-mode-text.aligned-text { color: #34d399; }
        body.dark-mode .alignment-mode-text.misaligned-text { color: #f87171; }

        .double-lip-switch {
            width: 48px; height: 24px; border-radius: 12px;
            background: linear-gradient(135deg, #10b981, #34d399);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.3);
        }
        .double-lip-switch-toggle {
            position: absolute; /* Added explicitly */
            top: 3px; left: 3px; width: 18px; height: 18px; border-radius: 50%;
            background: #ffffff; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); z-index: 2;
        }
        .double-lip-switch-toggle:after {
            content: ""; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 8px; height: 8px; border-radius: 50%;
            background: rgba(16, 185, 129, 0.2); transition: all 0.3s ease;
        }
        .double-lip-switch.active { background: linear-gradient(135deg, #ef4444, #f87171); box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.3); }
        .double-lip-switch.active .double-lip-switch-toggle { transform: translateX(24px); }
        .double-lip-switch.active .double-lip-switch-toggle:after { background: rgba(239, 68, 68, 0.2); }
        .double-lip-switch:not(.active):active .double-lip-switch-toggle { transform: scale(0.95); }
        .double-lip-switch.active:active .double-lip-switch-toggle { transform: translateX(24px) scale(0.95); }
        .double-lip-text { font-size: 0.875rem; transition: all 0.3s ease; margin-left: 0.25rem; min-width: 1.5rem; display: inline-block; text-align: left; }
        .double-lip-switch ~ .double-lip-text:before { content: "x1"; font-weight: 600; color: #10b981; }
        .double-lip-switch.active ~ .double-lip-text:before { content: "x2"; font-weight: 600; color: #ef4444; }
        body.dark-mode .double-lip-switch ~ .double-lip-text:before { color: #34d399; }
        body.dark-mode .double-lip-switch.active ~ .double-lip-text:before { color: #f87171; }

        .loading-overlay {
            position: fixed; top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex; align-items: center; justify-content: center; z-index: 2000;
            opacity: 0; visibility: hidden; transition: opacity 0.2s ease, visibility 0.2s ease;
        }
        body.dark-mode .loading-overlay { background-color: rgba(17, 24, 39, 0.7); }
        .loading-overlay.visible { opacity: 1; visibility: visible; }
        .loading-spinner {
            border: 5px solid var(--neutral-300); border-top: 5px solid var(--primary);
            border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite;
        }
        body.dark-mode .loading-spinner { border-color: var(--neutral-600); border-top-color: var(--primary-light); }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        /* --- Tab Styles --- */
        .tabs { display: flex; margin-bottom: 1.5rem; border-bottom: 2px solid var(--neutral-200); }
        body.dark-mode .tabs { border-bottom-color: var(--neutral-700); }
        .tab-button {
            padding: 0.75rem 1.5rem; /* Increased padding */
            cursor: pointer;
            border: none;
            background-color: transparent;
            font-size: 1rem; /* Slightly larger font */
            font-weight: 500;
            color: var(--neutral-500);
            margin-bottom: -2px; /* To align with the border-bottom of .tabs */
            border-bottom: 2px solid transparent;
            transition: color 0.2s ease, border-color 0.2s ease;
            outline: none;
            min-width: 100px; /* Add minimum width to stabilize tabs */
            text-align: center; /* Center text within the button */
        }
        .tab-button:hover { color: var(--primary); }
        .tab-button.active {
            color: var(--primary);
            border-bottom-color: var(--primary);
            font-weight: 600;
        }
        body.dark-mode .tab-button { color: var(--neutral-400); }
        body.dark-mode .tab-button:hover { color: var(--primary-light); }
        body.dark-mode .tab-button.active { color: var(--primary-light); border-bottom-color: var(--primary-light); }
        .tab-content { display: none; }
        .tab-content.active { display: block; animation: fadeIn 0.3s ease-out; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }

        /* --- Selected Components Summary Panel Styles (Header Version) --- */
        #summary-panel-container {
            position: relative; /* Changed from fixed */
            /* Removed bottom/right positioning */
            z-index: 1050;
            display: flex; /* Keep flex for alignment if needed */
            align-items: center; /* Align button vertically */
        }
        #summary-toggle-button {
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            width: 36px; /* Match toggle height */
            height: 36px; /* Match toggle height */
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15); /* Adjusted shadow */
            cursor: pointer;
            transition: var(--transition-transform), var(--transition-theme);
            border: none; /* Remove default border */
        }
        #summary-toggle-button:hover { transform: scale(1.1); background-color: var(--primary-dark); }
        #summary-toggle-button svg { width: 18px; height: 18px; } /* Adjusted icon size */
        #summary-panel {
            background-color: white;
            color: var(--neutral-800);
            border-radius: 0.5rem;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            padding: 1.5rem;
            position: fixed; /* Changed from absolute */
            top: 70px; /* To clear sticky header, adjust if header height changes */
            right: 0; /* Stick to the right edge of the viewport */
            /* Removed left, margin-left, margin-right */
            width: 320px; /* Reduced width for more compact display */
            max-height: 0; /* Initial state for animation */
            opacity: 0;
            overflow: hidden; /* Keep hidden initially for animation */
            visibility: hidden;
            transform: translateX(calc(100% + 1rem)) scale(0.95); /* Start off-screen to the right */
            transform-origin: top left; /* Animate from its top-left corner */
            transition: max-height var(--transition-duration) ease-out,
                        opacity var(--transition-duration) ease-out,
                        visibility 0s linear var(--transition-duration), /* Delay visibility until opacity transition fully ends */
                        transform var(--transition-duration) ease-out,
                        background-color var(--transition-duration) var(--transition-timing),
                        color var(--transition-duration) var(--transition-timing),
                        box-shadow var(--transition-duration) var(--transition-timing);
            z-index: 1040; /* Ensure it's above header, below modals */
            /* Performance optimizations to prevent flickering during scroll */
            will-change: transform, opacity, max-height;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            /* Prevent paint flashing during scroll */
            contain: paint;
            /* Force GPU acceleration */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        #summary-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            /* Prevent layout shifts and improve rendering */
            contain: layout;
            will-change: transform;
            transform: translateZ(0);
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
        }
        .summary-static-title {
            /* Only prevent unwanted animations while keeping language/theme transitions */
            /* Improve rendering with hardware acceleration */
            transform: translateZ(0);
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            /* Prevent layout shifts during scroll */
            contain: content;
            /* Ensure the element has a stable position */
            position: relative;
            z-index: 1;
        }
        #summary-close-button {
            background: none;
            border: none;
            color: var(--neutral-500);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 0.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-theme);
        }
        #summary-close-button:hover {
            color: var(--neutral-700);
            background-color: var(--neutral-200);
        }
        body.dark-mode #summary-close-button {
            color: var(--neutral-400);
        }
        body.dark-mode #summary-close-button:hover {
            color: var(--neutral-200);
            background-color: var(--neutral-700);
        }
        #summary-panel.visible {
            max-height: calc(100vh - 70px - 1rem); /* Max height considering header and some bottom margin */
            opacity: 1;
            visibility: visible;
            transform: translateX(0) scale(1); /* Slide into view */
            transition: max-height var(--transition-duration) ease-out,
                        opacity var(--transition-duration) ease-out,
                        visibility 0s linear 0s, /* Make visible immediately when starting to show */
                        transform var(--transition-duration) ease-out,
                        background-color var(--transition-duration) var(--transition-timing),
                        color var(--transition-duration) var(--transition-timing),
                        box-shadow var(--transition-duration) var(--transition-timing);
            overflow-y: auto; /* Add scroll if content overflows */
            /* Additional performance optimizations for visible state */
            contain: content; /* Improve rendering performance by isolating content */
        }
        body.dark-mode #summary-toggle-button {
            background-color: var(--primary-light);
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
        }
        body.dark-mode #summary-toggle-button:hover { background-color: var(--primary); }
        body.dark-mode #summary-panel {
            background-color: var(--neutral-800);
            color: var(--neutral-100);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .summary-item {
            margin-bottom: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px dashed var(--neutral-200);
            transition: border-color var(--transition-duration) var(--transition-timing);
            /* Performance optimizations */
            contain: content;
            will-change: transform;
            transform: translateZ(0);
        }
        body.dark-mode .summary-item { border-bottom-color: var(--neutral-700); }
        .summary-item:last-child { margin-bottom: 0; padding-bottom: 0; border-bottom: none; }
        .summary-item-label { font-size: 0.8rem; color: var(--neutral-500); transition: var(--transition-theme); }
        body.dark-mode .summary-item-label { color: var(--neutral-400); }
        .summary-item-value { font-weight: 500; word-break: break-all; transition: var(--transition-theme); }
        .summary-total {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--neutral-300);
            transition: border-color var(--transition-duration) var(--transition-timing);
            /* Performance optimizations */
            contain: content;
            will-change: transform;
            transform: translateZ(0);
        }
        body.dark-mode .summary-total { border-top-color: var(--neutral-600); }
        .summary-total-label { font-weight: 600; font-size: 1rem; transition: var(--transition-theme); }
        .summary-total-value { font-weight: 700; font-size: 1.125rem; color: var(--primary); transition: var(--transition-theme); }
        body.dark-mode .summary-total-value { color: var(--primary-light); }

        /* "Select this Option" button specific style */
        .btn-select-option { /* Use btn-success styling as base */
            background-color: var(--success); color: white; border: 1px solid #059669;
        }
        .btn-select-option:hover { background-color: #059669; }
        .btn-select-option.selected {
            background-color: var(--accent); color: white; border-color: #d97706; /* Amber-600 */
        }
        .btn-select-option.selected:hover { background-color: #d97706; }

        /* Accent button style for selected options */
        .btn-accent {
            background-color: var(--accent); color: white; border: 1px solid #d97706; /* Amber-600 */
        }
        .btn-accent:hover { background-color: #d97706; }
    </style>
</head>
<body> <!-- Removed pt-24 -->
    <header class="sticky top-0 left-0 right-0 z-[1000] bg-white shadow-md w-full py-2"> <!-- Header background is full width -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between"> <!-- Inner container for content alignment -->
            <!-- Tabs Group (Left) -->
            <div class="tabs !mb-0 !border-b-0 flex items-center">
                <button class="tab-button active" data-tab="innerText" data-translate-key="tabInnerText">Inner Text</button>
                <button class="tab-button" data-tab="cover" data-translate-key="tabCover">Cover</button>
                <button class="tab-button" data-tab="endpapers" data-translate-key="tabEndpapers">Endpapers</button>
            </div>
            <!-- Controls Group (Right) -->
            <div class="flex items-center space-x-4" id="new-header-controls">
                <div id="language-switcher" class="lang-switch" title="Switch Language">
                     <span class="lang-switch-label en">EN</span>
                     <div class="lang-switch-toggle"></div>
                         <span class="lang-switch-label zh">中</span>
                     </div>
                     <div id="theme-switcher" class="theme-switch" title="Switch Theme">
                         <svg class="theme-icon sun" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"/></svg>
                         <div class="theme-switch-toggle"></div>
                         <svg class="theme-icon moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path fill-rule="evenodd" d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" clip-rule="evenodd"/></svg>
                     </div>
                     <!-- Moved Shopping Cart Container Here -->
                     <div id="summary-panel-container" class="relative">
                         <button id="summary-toggle-button" title="View Selected Components Summary" class="relative z-10"> <!-- Ensure button is clickable -->
                             <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                 <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                             </svg>
                         </button>
                         <div id="summary-panel"> <!-- Removed absolute positioning classes, handled by CSS -->
                             <div id="summary-panel-header">
                                 <h3 class="text-lg font-semibold summary-static-title" data-translate-key="summaryTitle">Selected Components</h3>
                                 <button id="summary-close-button" title="Close Summary Panel">
                                     <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                         <line x1="18" y1="6" x2="6" y2="18"></line>
                                         <line x1="6" y1="6" x2="18" y2="18"></line>
                                     </svg>
                                 </button>
                             </div>
                             <div id="summary-content" style="contain: content; will-change: transform; transform: translateZ(0);">
                                 <p class="text-sm text-neutral-500" data-translate-key="summaryNoSelection">No components selected yet.</p>
                             </div>
                             <div class="summary-total mt-4">
                                 <div class="flex justify-between items-center">
                                     <span class="summary-total-label" data-translate-key="summaryTotalCostPerBook">Total Cost/Book:</span>
                                     <span id="summary-total-cost" class="summary-total-value">$0.0000</span>
                                 </div>
                             </div>
                         </div>
                     </div>
                </div>
            </div>
        </div>
    </header>
    <div class="max-w-7xl mx-auto mt-6 px-4 md:px-6"> <!-- Added horizontal padding here, kept mt-6 -->
        <!-- Old header content removed, title/subtitle gone -->
        <!-- Original Tabs Navigation removed from here -->

        <!-- Tab Content Area -->
        <div id="tab-content-area">
            <!-- Inner Text Tab Content -->
            <div id="innerText-tab-content" class="tab-content active">
                <div class="grid grid-cols-1 lg:grid-cols-3 lg:gap-6">
                    <div class="card full-height">
                        <div class="card-header">
                            <h2 class="text-xl font-semibold" data-translate-key="jobSpecsTitle">Job Specifications</h2>
                        </div>
                        <div class="space-y-4">
                            <div>
                                <div class="grid grid-cols-2 gap-x-4 mb-1">
                                     <label for="trim_h_innerText" class="form-label" data-translate-key="pageHeightLabel">Page Height (mm)</label>
                                     <label for="trim_h_in_innerText" class="form-label" data-translate-key="pageHeightLabelIn">Page Height (in)</label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="relative flex-1">
                                        <input type="number" id="trim_h_innerText" class="form-input pr-8" value="225" step="0.1" tabindex="1" placeholder="e.g., 225" data-placeholder-translate-key="placeholderPageHeight">
                                        <span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">mm</span>
                                    </div>
                                    <svg class="w-6 h-6 conversion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg>
                                    <div class="relative flex-1">
                                        <input type="number" id="trim_h_in_innerText" class="form-input pr-8" step="0.01" tabindex="3" placeholder="e.g., 8.86" data-placeholder-translate-key="placeholderPageHeightIn">
                                        <span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">in</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="grid grid-cols-2 gap-x-4 mb-1">
                                     <label for="trim_w_innerText" class="form-label" data-translate-key="pageWidthLabel">Page Width (mm)</label>
                                     <label for="trim_w_in_innerText" class="form-label" data-translate-key="pageWidthLabelIn">Page Width (in)</label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="relative flex-1">
                                        <input type="number" id="trim_w_innerText" class="form-input pr-8" value="150" step="0.1" tabindex="2" placeholder="e.g., 150" data-placeholder-translate-key="placeholderPageWidth">
                                        <span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">mm</span>
                                    </div>
                                    <svg class="w-6 h-6 conversion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg>
                                    <div class="relative flex-1">
                                        <input type="number" id="trim_w_in_innerText" class="form-input pr-8" step="0.01" tabindex="4" placeholder="e.g., 5.91" data-placeholder-translate-key="placeholderPageWidthIn">
                                        <span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">in</span>
                                    </div>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="total_pages_innerText" class="form-label" data-translate-key="totalPagesLabel">Total Pages</label>
                                    <input type="number" id="total_pages_innerText" class="form-input" value="320" step="2" tabindex="5" placeholder="e.g., 320" data-placeholder-translate-key="placeholderTotalPages">
                                </div>
                                <div>
                                    <label for="quantity_innerText" class="form-label" data-translate-key="quantityLabel">Quantity</label>
                                    <input type="number" id="quantity_innerText" class="form-input" value="1000" tabindex="6" placeholder="e.g., 1000" data-placeholder-translate-key="placeholderQuantity">
                                </div>
                                <div>
                                    <label for="binding_innerText" class="form-label" data-translate-key="bindingLabel">Binding Method</label>
                                    <select id="binding_innerText" class="form-select" tabindex="7">
                                        <option value="saddleStitch" data-translate-key="bindingSaddleStitch" selected>Saddle Stitch</option>
                                        <option value="perfectBound" data-translate-key="bindingPerfectBound">Paperback</option>
                                        <option value="wireO" data-translate-key="bindingWireO">Wire-O / Coil</option>
                                        <option value="sectionSewn" data-translate-key="bindingSectionSewn">Section Sewn</option>
                                        <option value="caseBound" data-translate-key="bindingCaseBound">Case Bound (Hardcover)</option>
                                        <option value="singlePage" data-translate-key="bindingSinglePage">Single Page / Flat Sheet</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="spoilage_pct_innerText" class="form-label" data-translate-key="spoilageLabel">Spoilage (%)</label>
                                    <input type="number" id="spoilage_pct_innerText" class="form-input" value="5" step="0.1" tabindex="8" placeholder="e.g., 5" data-placeholder-translate-key="placeholderSpoilage">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card full-height">
                        <div class="card-header">
                            <h2 class="text-xl font-semibold" data-translate-key="prodParamsTitle">Production Parameters</h2>
                        </div>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="form-label" data-translate-key="bleedLabel">Bleed (mm)</label>
                                <input type="number" id="bleed_innerText" class="form-input" value="3" tabindex="9" placeholder="e.g., 3" data-placeholder-translate-key="placeholderBleed">
                            </div>
                            <div>
                                <label class="form-label" data-translate-key="gripperLabel">Gripper (mm)</label>
                                <input type="number" id="gripper_innerText" class="form-input" value="12" tabindex="10" placeholder="e.g., 12" data-placeholder-translate-key="placeholderGripper">
                            </div>
                            <div>
                                <label class="form-label" data-translate-key="colorBarLabel">Color Bar (mm)</label>
                                <input type="number" id="color_bar_innerText" class="form-input" value="6" tabindex="11" placeholder="e.g., 6" data-placeholder-translate-key="placeholderColorBar">
                            </div>
                            <div>
                                <label class="form-label" data-translate-key="lipLabel">Side Lip (mm)</label>
                                <div class="flex items-center gap-2">
                                    <input type="number" id="lip_innerText" class="form-input flex-1" value="5" tabindex="12" placeholder="e.g., 5" data-placeholder-translate-key="placeholderLip">
                                    <div id="double-lip-switcher_innerText" class="double-lip-switch" title="Double Side Lip" data-translate-key="doubleLipTitle" tabindex="13">
                                        <div class="double-lip-switch-toggle"></div>
                                    </div>
                                    <span class="double-lip-text"></span>
                                </div>
                            </div>
                            <div class="col-span-2">
                                <label class="form-label" data-translate-key="alignmentModeLabel">Grain Alignment</label>
                                <div class="flex items-center">
                                    <div id="alignment-mode-switcher_innerText" class="alignment-switch" title="Switch Grain Alignment" tabindex="14">
                                        <div class="alignment-switch-toggle"></div>
                                    </div>
                                    <span class="ml-2 text-sm">
                                        <span class="alignment-mode-text misaligned-text" data-translate-key="alignmentModeMisaligned">Misaligned</span>
                                        <span class="alignment-mode-text aligned-text" data-translate-key="alignmentModeAligned">Aligned</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                         <p class="text-xs text-neutral-500 mt-auto dark-mode-text" data-translate-key="prodParamsNote">* Gutter not used for sheet area. Margins applied to 720H x 1020W press.</p>
                    </div>
                    <div class="card full-height">
                        <div class="card-header"> <h2 class="text-xl font-semibold" data-translate-key="converterTitle">Unit Converter</h2> </div>
                        <div class="space-y-4 p-2">
                            <div>
                                <label class="block text-sm font-medium dark-mode-text mb-1" data-translate-key="converterWeightTypeLabel">Paper Weight Type</label>
                                <div class="flex space-x-4 mb-2">
                                    <label class="inline-flex items-center"> <input type="radio" class="form-radio h-3.8 w-3.8 text-primary focus:ring-primary-light" name="paperWeightType_innerText" value="Text" checked> <span class="ml-2 text-sm dark-mode-text" data-translate-key="converterWeightTypeText">Text</span> </label>
                                    <label class="inline-flex items-center"> <input type="radio" class="form-radio h-3.8 w-3.8 text-primary focus:ring-primary-light" name="paperWeightType_innerText" value="Cover"> <span class="ml-2 text-sm dark-mode-text" data-translate-key="converterWeightTypeCover">Cover</span> </label>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div> <label for="converter_lbs_innerText" class="form-label" data-translate-key="converterLbsLabel">Pounds (lbs)</label> <div class="relative"> <input type="number" id="converter_lbs_innerText" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">lbs</span> </div> </div>
                                    <div> <label for="converter_gsm_innerText" class="form-label" data-translate-key="converterGsmLabel">GSM (g/m²)</label> <div class="relative"> <input type="number" id="converter_gsm_innerText" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">g/m²</span> </div> </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium dark-mode-text mb-1" data-translate-key="converterThicknessLabel">Thickness</label>
                                <div class="grid grid-cols-2 gap-3 mb-2">
                                    <div> <label for="converter_pt_innerText" class="form-label" data-translate-key="converterPtLabel">Points (pt)</label> <div class="relative"> <input type="number" id="converter_pt_innerText" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">pt</span> </div> </div>
                                    <div> <label for="converter_microns_innerText" class="form-label" data-translate-key="converterMicronsLabel">Microns (µm)</label> <div class="relative"> <input type="number" id="converter_microns_innerText" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">µm</span> </div> </div>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div> <label for="converter_in_innerText" class="form-label" data-translate-key="converterInLabel">Inches (in)</label> <div class="relative"> <input type="number" id="converter_in_innerText" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">in</span> </div> </div>
                                    <div> <label for="converter_mm_innerText" class="form-label" data-translate-key="converterMmLabel">Millimeters (mm)</label> <div class="relative"> <input type="number" id="converter_mm_innerText" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">mm</span> </div> </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header collapsible" id="paper-options-header_innerText">
                        <div> <h2 class="text-xl font-semibold" data-translate-key="paperOptionsTitle">Paper Options</h2> </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 toggle-icon" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /> </svg>
                    </div>
                    <div class="card-content" id="paper-options-content_innerText">
                        <p class="text-sm text-neutral-600 mb-3 dark-mode-text" data-translate-key="paperOptionsNote">Enter full original sheet dimensions. Sheets > 720x1020mm will be rotated/trimmed.</p>
                        <div class="table-container">
                            <table id="paper-options-table_innerText">
                                <thead> <tr> <th class="th-paper-name" data-translate-key="colPaperName">Paper Name</th> <th class="th-source" data-translate-key="colSource">Source</th> <th class="th-sheet-h" data-translate-key="colSheetH">Sheet H (mm)</th> <th class="th-sheet-w" data-translate-key="colSheetW">Sheet W (mm)</th> <th class="th-grain-dir" data-translate-key="colGrainDir">Grain || To</th> <th class="th-caliper" data-translate-key="colCaliper">Caliper (µm)</th> <th class="th-cost-ream" data-translate-key="colCostReam">Cost/Ream ($)</th> <th class="th-gsm" data-translate-key="colGsm">GSM (g/m²)</th> <th class="th-cost-tonne" data-translate-key="colCostTonne">Cost/Ton ($)</th> <th class="th-actions" data-translate-key="colActions">Actions</th> </tr> </thead>
                                <tbody>
                                    <!-- Default Inner Text Paper Rows -->
                                    <tr data-row-id="1"> <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Halved 31 x 43&quot; 对开"></td> <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll">Roll</option></select></td> <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="546.1"></td> <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="787.4"></td> <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>Height</option><option value="Width" data-translate-key="grainWidth">Width</option></select></td> <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="100" placeholder="µm"></td> <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="50"></td> <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="80"></td> <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value=""></td> <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td> </tr>
                                    <tr data-row-id="2"> <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Halved 35 x 47&quot; 对开"></td> <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll">Roll</option></select></td> <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="596.9"></td> <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="889.0"></td> <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>Height</option><option value="Width" data-translate-key="grainWidth">Width</option></select></td> <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="100" placeholder="µm"></td> <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="22.50"></td> <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="80"></td> <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value=""></td> <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td> </tr>
                                    <tr data-row-id="3"> <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Quarter 35 x 47&quot; 四开"></td> <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll">Roll</option></select></td> <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="444.5"></td> <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="596.9"></td> <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight">Height</option><option value="Width" data-translate-key="grainWidth" selected>Width</option></select></td> <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="100" placeholder="µm"></td> <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="11.25"></td> <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="80"></td> <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value=""></td> <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td> </tr>
                                    <tr data-row-id="4"> <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Special 25 x 38&quot;"></td> <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll">Roll</option></select></td> <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="635.0"></td> <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="965.2"></td> <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight">Height</option><option value="Width" data-translate-key="grainWidth" selected>Width</option></select></td> <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="100" placeholder="µm"></td> <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="40"></td> <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="80"></td> <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value=""></td> <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td> </tr>
                                    <tr data-row-id="5"> <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Custom 25&quot; Roll"></td> <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut">Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll" selected>Roll</option></select></td> <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="" data-translate-key="placeholderRollH" placeholder="≤ 720"></td> <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="635.0" data-translate-key="placeholderRollW" placeholder="≤ 1020"></td> <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>Height</option><option value="Width" data-translate-key="grainWidth">Width</option></select></td> <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="100" placeholder="µm"></td> <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value=""></td> <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="80"></td> <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value="1200"></td> <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td> </tr>
                                    <tr data-row-id="6"> <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Custom 31&quot; Roll"></td> <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut">Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll" selected>Roll</option></select></td> <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="" data-translate-key="placeholderRollH" placeholder="≤ 720"></td> <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="787.4" data-translate-key="placeholderRollW" placeholder="≤ 1020"></td> <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>Height</option><option value="Width" data-translate-key="grainWidth">Width</option></select></td> <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="100" placeholder="µm"></td> <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value=""></td> <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="80"></td> <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value="1200"></td> <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td> </tr>
                                    <tr data-row-id="7"> <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Custom 35&quot; Roll"></td> <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut">Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll" selected>Roll</option></select></td> <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="" data-translate-key="placeholderRollH" placeholder="≤ 720"></td> <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="889.0" data-translate-key="placeholderRollW" placeholder="≤ 1020"></td> <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>Height</option><option value="Width" data-translate-key="grainWidth">Width</option></select></td> <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="100" placeholder="µm"></td> <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value=""></td> <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="80"></td> <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value="1200"></td> <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td> </tr>
                                    <tr data-row-id="8"> <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Custom 38&quot; Roll"></td> <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut">Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll" selected>Roll</option></select></td> <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="" data-translate-key="placeholderRollH" placeholder="≤ 720"></td> <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="965.2" data-translate-key="placeholderRollW" placeholder="≤ 1020"></td> <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>Height</option><option value="Width" data-translate-key="grainWidth">Width</option></select></td> <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="100" placeholder="µm"></td> <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value=""></td> <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="80"></td> <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value="1200"></td> <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td> </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4 flex justify-between items-center">
                            <button id="add-paper-option_innerText" class="btn btn-secondary add-paper-option-btn"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /> </svg> <span data-translate-key="addOptionBtn">Add Option</span> </button>
                            <button id="calculate-btn_innerText" class="btn btn-primary calculate-btn" tabindex="15"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00.076-1.822l-10-4z" clip-rule="evenodd" /></svg> <span data-translate-key="calculateBtn">Calculate Costs</span> </button>
                        </div>
                    </div>
                </div>
                <div id="results-section_innerText" class="card hidden results-section-card">
                    <div class="card-header collapsible" id="results-header_innerText">
                        <div><h2 class="text-xl font-semibold" data-translate-key="resultsTitle">Calculation Results</h2></div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 toggle-icon" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>
                    </div>
                    <div id="results-content_innerText" class="card-content results-content-card">
                        <div id="results-container_innerText" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 results-container-div"></div>
                    </div>
                </div>
            </div> <!-- End Inner Text Tab -->

            <!-- Cover Tab Content -->
            <div id="cover-tab-content" class="tab-content">
                <div class="grid grid-cols-1 lg:grid-cols-3 lg:gap-6">
                    <div class="card full-height">
                        <div class="card-header"> <h2 class="text-xl font-semibold" data-translate-key="coverSpecsTitle">Cover Specifications</h2> </div>
                        <div class="space-y-4">
                            <div>
                                <div class="grid grid-cols-2 gap-x-4 mb-1">
                                     <label for="cover_trim_h_cover" class="form-label" data-translate-key="coverHeightLabel">Cover Height (mm)</label>
                                     <label for="cover_trim_h_in_cover" class="form-label" data-translate-key="coverHeightLabelIn">Cover Height (in)</label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="relative flex-1"><input type="number" id="cover_trim_h_cover" class="form-input pr-8" step="0.1" placeholder="e.g., 225" data-placeholder-translate-key="placeholderCoverHeight"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">mm</span></div>
                                    <svg class="w-6 h-6 conversion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg>
                                    <div class="relative flex-1"><input type="number" id="cover_trim_h_in_cover" class="form-input pr-8" step="0.01" placeholder="e.g., 8.86" data-placeholder-translate-key="placeholderCoverHeightIn"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">in</span></div>
                                </div>
                            </div>
                            <div>
                                <div class="grid grid-cols-2 gap-x-4 mb-1">
                                     <label for="cover_trim_w_cover" class="form-label" data-translate-key="coverWidthLabel">Cover Width (mm)</label>
                                     <label for="cover_trim_w_in_cover" class="form-label" data-translate-key="coverWidthLabelIn">Cover Width (in)</label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="relative flex-1"><input type="number" id="cover_trim_w_cover" class="form-input pr-8" step="0.1" placeholder="e.g., 150" data-placeholder-translate-key="placeholderCoverWidth" title="Typically same as Inner Text Page Width" data-title-translate-key="coverDimNote"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">mm</span></div>
                                    <svg class="w-6 h-6 conversion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg>
                                    <div class="relative flex-1"><input type="number" id="cover_trim_w_in_cover" class="form-input pr-8" step="0.01" placeholder="e.g., 5.91" data-placeholder-translate-key="placeholderCoverWidthIn" title="Typically same as Inner Text Page Width" data-title-translate-key="coverDimNote"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">in</span></div>
                                </div>
                            </div>
                            <div>
                                <div class="grid grid-cols-2 gap-x-4 mb-1">
                                     <label for="spine_thickness_cover" class="form-label" data-translate-key="spineThicknessLabel">Spine Thickness (mm)</label>
                                     <label for="spine_thickness_in_cover" class="form-label" data-translate-key="spineThicknessLabelIn">Spine Thickness (in)</label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="relative flex-1"><input type="number" id="spine_thickness_cover" class="form-input pr-8" step="0.1" placeholder="e.g., 15 or auto" data-placeholder-translate-key="placeholderSpineThickness"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">mm</span></div>
                                    <svg class="w-6 h-6 conversion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg>
                                    <div class="relative flex-1"><input type="number" id="spine_thickness_in_cover" class="form-input pr-8" step="0.01" placeholder="e.g., 0.59 or auto" data-placeholder-translate-key="placeholderSpineThicknessIn"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">in</span></div>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="cover_type_cover" class="form-label" data-translate-key="coverTypeLabel">Cover Type</label>
                                    <select id="cover_type_cover" class="form-select">
                                        <option value="paperback" data-translate-key="coverTypePaperback" selected>Paperback</option>
                                        <option value="hardcover" data-translate-key="coverTypeHardcover">Hardcover (Case)</option>
                                        <option value="dustJacket" data-translate-key="coverTypeDustJacket">Dust Jacket</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="quantity_cover" class="form-label" data-translate-key="quantityLabel">Quantity</label>
                                    <input type="number" id="quantity_cover" class="form-input" value="" placeholder="e.g., 1000" data-placeholder-translate-key="placeholderQuantity">
                                </div>
                                <div>
                                    <label for="spoilage_pct_cover" class="form-label" data-translate-key="spoilageLabel">Spoilage (%)</label>
                                    <input type="number" id="spoilage_pct_cover" class="form-input" value="7" step="0.1" placeholder="e.g., 7" data-placeholder-translate-key="placeholderSpoilage">
                                </div>
                                <div id="flap-width-section_cover" class="hidden">
                                    <label for="flap_width_cover" class="form-label" data-translate-key="flapWidthLabel">Flap Width (mm)</label>
                                    <input type="number" id="flap_width_cover" class="form-input" value="" step="0.1" placeholder="e.g., half book width" data-placeholder-translate-key="placeholderFlapWidth" title="Typically half the book width. Auto-calculated by default." data-title-translate-key="flapWidthNote">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card full-height">
                        <div class="card-header"> <h2 class="text-xl font-semibold" data-translate-key="coverProdParamsTitle">Cover Prod. Parameters</h2> </div>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div> <label class="form-label" data-translate-key="bleedLabel">Bleed (mm)</label> <input type="number" id="bleed_cover" class="form-input" value="5" placeholder="e.g., 5" data-placeholder-translate-key="placeholderBleed"> </div>
                            <div> <label class="form-label" data-translate-key="gripperLabel">Gripper (mm)</label> <input type="number" id="gripper_cover" class="form-input" value="12" placeholder="e.g., 12" data-placeholder-translate-key="placeholderGripper"> </div>
                            <div> <label class="form-label" data-translate-key="colorBarLabel">Color Bar (mm)</label> <input type="number" id="color_bar_cover" class="form-input" value="6" placeholder="e.g., 6" data-placeholder-translate-key="placeholderColorBar"> </div>

                            <div id="turn-in-allowance-section_cover" class="hidden">
                                <label for="turn_in_allowance_cover" class="form-label" data-translate-key="turnInAllowanceLabel">Turn-in Allowance (mm)</label>
                                <input type="number" id="turn_in_allowance_cover" class="form-input" value="18" step="0.1" placeholder="e.g., 18" data-placeholder-translate-key="placeholderTurnInAllowance">
                                <p class="text-xs text-neutral-500 mt-1 dark-mode-text" data-translate-key="turnInAllowanceNote">Standard is 15-20mm for hardcover turn-ins.</p>
                            </div>

                             <div class="col-span-2">
                                <label class="form-label" data-translate-key="alignmentModeLabel">Grain Alignment</label>
                                <div class="flex items-center">
                                    <div id="alignment-mode-switcher_cover" class="alignment-switch" title="Switch Grain Alignment">
                                        <div class="alignment-switch-toggle"></div>
                                    </div>
                                    <span class="ml-2 text-sm">
                                        <span class="alignment-mode-text misaligned-text" data-translate-key="alignmentModeMisaligned">Misaligned</span>
                                        <span class="alignment-mode-text aligned-text" data-translate-key="alignmentModeAligned">Aligned</span>
                                    </span>
                                    <span class="ml-2" title="Grain typically || to spine/cover height" data-title-translate-key="coverGrainNote">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </span>
                                </div>
                            </div>
                        </div>
                         <p class="text-xs text-neutral-500 mt-auto dark-mode-text" data-translate-key="prodParamsNote">* Gutter not used for sheet area. Margins applied to 720H x 1020W press.</p>
                    </div>
                     <div class="card full-height"> <!-- Unit Converter for Cover (can be simplified or share logic) -->
                        <div class="card-header"> <h2 class="text-xl font-semibold" data-translate-key="converterTitle">Unit Converter</h2> </div>
                        <div class="space-y-4 p-2">
                            <div>
                                <label class="block text-sm font-medium dark-mode-text mb-1" data-translate-key="converterWeightTypeLabel">Paper Weight Type</label>
                                <div class="flex space-x-4 mb-2">
                                    <label class="inline-flex items-center"> <input type="radio" class="form-radio h-3.8 w-3.8 text-primary focus:ring-primary-light" name="paperWeightType_cover" value="Text" > <span class="ml-2 text-sm dark-mode-text" data-translate-key="converterWeightTypeText">Text</span> </label>
                                    <label class="inline-flex items-center"> <input type="radio" class="form-radio h-3.8 w-3.8 text-primary focus:ring-primary-light" name="paperWeightType_cover" value="Cover" checked> <span class="ml-2 text-sm dark-mode-text" data-translate-key="converterWeightTypeCover">Cover</span> </label>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div> <label for="converter_lbs_cover" class="form-label" data-translate-key="converterLbsLabel">Pounds (lbs)</label> <div class="relative"> <input type="number" id="converter_lbs_cover" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">lbs</span> </div> </div>
                                    <div> <label for="converter_gsm_cover" class="form-label" data-translate-key="converterGsmLabel">GSM (g/m²)</label> <div class="relative"> <input type="number" id="converter_gsm_cover" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">g/m²</span> </div> </div>
                                </div>
                            </div>
                             <div>
                                <label class="block text-sm font-medium dark-mode-text mb-1" data-translate-key="converterThicknessLabel">Thickness</label>
                                <div class="grid grid-cols-2 gap-3 mb-2">
                                    <div> <label for="converter_pt_cover" class="form-label" data-translate-key="converterPtLabel">Points (pt)</label> <div class="relative"> <input type="number" id="converter_pt_cover" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">pt</span> </div> </div>
                                    <div> <label for="converter_microns_cover" class="form-label" data-translate-key="converterMicronsLabel">Microns (µm)</label> <div class="relative"> <input type="number" id="converter_microns_cover" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">µm</span> </div> </div>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div> <label for="converter_in_cover" class="form-label" data-translate-key="converterInLabel">Inches (in)</label> <div class="relative"> <input type="number" id="converter_in_cover" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">in</span> </div> </div>
                                    <div> <label for="converter_mm_cover" class="form-label" data-translate-key="converterMmLabel">Millimeters (mm)</label> <div class="relative"> <input type="number" id="converter_mm_cover" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">mm</span> </div> </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                 <div class="card">
                    <div class="card-header collapsible" id="paper-options-header_cover">
                        <div> <h2 class="text-xl font-semibold" data-translate-key="paperOptionsTitleCover">Cover Paper Options</h2> </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 toggle-icon" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /> </svg>
                    </div>
                    <div class="card-content" id="paper-options-content_cover">
                        <p class="text-sm text-neutral-600 mb-3 dark-mode-text" data-translate-key="paperOptionsNote">Enter full original sheet dimensions. Sheets > 720x1020mm will be rotated/trimmed.</p>
                        <div class="table-container">
                            <table id="paper-options-table_cover">
                                <thead> <tr> <th class="th-paper-name" data-translate-key="colPaperName">Paper Name</th> <th class="th-source" data-translate-key="colSource">Source</th> <th class="th-sheet-h" data-translate-key="colSheetH">Sheet H (mm)</th> <th class="th-sheet-w" data-translate-key="colSheetW">Sheet W (mm)</th> <th class="th-grain-dir" data-translate-key="colGrainDir">Grain || To</th> <th class="th-caliper" data-translate-key="colCaliper">Caliper (µm)</th> <th class="th-cost-ream" data-translate-key="colCostReam">Cost/Ream ($)</th> <th class="th-gsm" data-translate-key="colGsm">GSM (g/m²)</th> <th class="th-cost-tonne" data-translate-key="colCostTonne">Cost/Ton ($)</th> <th class="th-actions" data-translate-key="colActions">Actions</th> </tr> </thead>
                                <tbody>
                                    <tr data-row-id="1">
                                        <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Art Card 250gsm"></td>
                                        <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll">Roll</option></select></td>
                                        <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="635"></td>
                                        <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="965"></td>
                                        <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>Height</option><option value="Width" data-translate-key="grainWidth">Width</option></select></td>
                                        <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="280" placeholder="µm"></td>
                                        <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="120"></td>
                                        <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="250"></td>
                                        <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value=""></td>
                                        <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td>
                                    </tr>
                                    <tr data-row-id="2">
                                        <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Coated Board 300gsm"></td>
                                        <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll">Roll</option></select></td>
                                        <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="700"></td>
                                        <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="1000"></td>
                                        <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Width" data-translate-key="grainWidth" selected>Width</option><option value="Height" data-translate-key="grainHeight">Height</option></select></td>
                                        <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="350" placeholder="µm"></td>
                                        <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="150"></td>
                                        <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="300"></td>
                                        <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value=""></td>
                                        <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4 flex justify-between items-center">
                            <button id="add-paper-option_cover" class="btn btn-secondary add-paper-option-btn"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /> </svg> <span data-translate-key="addOptionBtn">Add Option</span> </button>
                            <button id="calculate-btn_cover" class="btn btn-primary calculate-btn"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00-.076-1.822l-10-4z" clip-rule="evenodd" /></svg> <span data-translate-key="calculateBtnCover">Calculate Cover Costs</span> </button>
                        </div>
                    </div>
                </div>
                <div id="results-section_cover" class="card hidden results-section-card">
                    <div class="card-header collapsible" id="results-header_cover">
                        <div><h2 class="text-xl font-semibold" data-translate-key="resultsTitleCover">Cover Results</h2></div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 toggle-icon" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>
                    </div>
                    <div id="results-content_cover" class="card-content results-content-card">
                        <div id="results-container_cover" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 results-container-div"></div>
                    </div>
                </div>
            </div> <!-- End Cover Tab -->

            <!-- Endpapers Tab Content -->
            <div id="endpapers-tab-content" class="tab-content">
                 <div class="grid grid-cols-1 lg:grid-cols-3 lg:gap-6">
                    <div class="card full-height">
                        <div class="card-header"> <h2 class="text-xl font-semibold" data-translate-key="endpapersSpecsTitle">Endpaper Specifications</h2> </div>
                        <div class="space-y-4">
                             <div>
                                <div class="grid grid-cols-2 gap-x-4 mb-1">
                                     <label for="trim_h_endpapers" class="form-label" data-translate-key="endpaperHeightLabel">Endpaper Height (mm)</label>
                                     <label for="trim_h_in_endpapers" class="form-label" data-translate-key="endpaperHeightLabelIn">Endpaper Height (in)</label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="relative flex-1"><input type="number" id="trim_h_endpapers" class="form-input pr-8" step="0.1" placeholder="e.g., 225" data-placeholder-translate-key="placeholderEndpaperHeight"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">mm</span></div>
                                    <svg class="w-6 h-6 conversion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg>
                                    <div class="relative flex-1"><input type="number" id="trim_h_in_endpapers" class="form-input pr-8" step="0.01" placeholder="e.g., 8.86" data-placeholder-translate-key="placeholderEndpaperHeightIn"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">in</span></div>
                                </div>
                            </div>
                             <div>
                                <div class="grid grid-cols-2 gap-x-4 mb-1">
                                     <label for="trim_w_endpapers" class="form-label" data-translate-key="endpaperWidthLabel">Endpaper Width (mm)</label>
                                     <label for="trim_w_in_endpapers" class="form-label" data-translate-key="endpaperWidthLabelIn">Endpaper Width (in)</label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="relative flex-1"><input type="number" id="trim_w_endpapers" class="form-input pr-8" step="0.1" placeholder="e.g., 150" data-placeholder-translate-key="placeholderEndpaperWidth" title="Typically same as Inner Text Page Width" data-title-translate-key="coverDimNote"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">mm</span></div>
                                    <svg class="w-6 h-6 conversion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg>
                                    <div class="relative flex-1"><input type="number" id="trim_w_in_endpapers" class="form-input pr-8" step="0.01" placeholder="e.g., 5.91" data-placeholder-translate-key="placeholderEndpaperWidthIn" title="Typically same as Inner Text Page Width" data-title-translate-key="coverDimNote"><span class="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-neutral-500">in</span></div>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="endpaper_type_endpapers" class="form-label" data-translate-key="endpaperTypeLabel">Endpaper Type</label>
                                    <select id="endpaper_type_endpapers" class="form-select">
                                        <option value="single" data-translate-key="endpaperTypeSingle" selected>Single Leaf (4pp per book)</option>
                                        <option value="double" data-translate-key="endpaperTypeDouble">Double Leaf (8pp per book)</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="quantity_endpapers" class="form-label" data-translate-key="quantityLabel">Quantity</label>
                                    <input type="number" id="quantity_endpapers" class="form-input" value="" placeholder="e.g., 1000" data-placeholder-translate-key="placeholderQuantity">
                                </div>
                                <div>
                                    <label for="spoilage_pct_endpapers" class="form-label" data-translate-key="spoilageLabel">Spoilage (%)</label>
                                    <input type="number" id="spoilage_pct_endpapers" class="form-input" value="10" step="0.1" placeholder="e.g., 10" data-placeholder-translate-key="placeholderSpoilage">
                                </div>
                            </div>
                        </div>
                    </div>
                     <div class="card full-height">
                        <div class="card-header"> <h2 class="text-xl font-semibold" data-translate-key="endpaperProdParamsTitle">Endpaper Prod. Parameters</h2> </div>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div> <label class="form-label" data-translate-key="bleedLabel">Bleed (mm)</label> <input type="number" id="bleed_endpapers" class="form-input" value="3" placeholder="e.g., 3" data-placeholder-translate-key="placeholderBleed"> </div>
                            <div> <label class="form-label" data-translate-key="gripperLabel">Gripper (mm)</label> <input type="number" id="gripper_endpapers" class="form-input" value="12" placeholder="e.g., 12" data-placeholder-translate-key="placeholderGripper"> </div>
                            <div> <label class="form-label" data-translate-key="colorBarLabel">Color Bar (mm)</label> <input type="number" id="color_bar_endpapers" class="form-input" value="6" placeholder="e.g., 6" data-placeholder-translate-key="placeholderColorBar"> </div>
                            <div class="col-span-2">
                                <label class="form-label" data-translate-key="alignmentModeLabel">Grain Alignment</label>
                                <div class="flex items-center">
                                    <div id="alignment-mode-switcher_endpapers" class="alignment-switch" title="Switch Grain Alignment">
                                        <div class="alignment-switch-toggle"></div>
                                    </div>
                                    <span class="ml-2 text-sm">
                                        <span class="alignment-mode-text misaligned-text" data-translate-key="alignmentModeMisaligned">Misaligned</span>
                                        <span class="alignment-mode-text aligned-text" data-translate-key="alignmentModeAligned">Aligned</span>
                                    </span>
                                    <span class="ml-2" title="Grain typically || to spine/endpaper height" data-title-translate-key="coverGrainNote">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </span>
                                </div>
                            </div>
                        </div>
                         <p class="text-xs text-neutral-500 mt-auto dark-mode-text" data-translate-key="prodParamsNote">* Gutter not used for sheet area. Margins applied to 720H x 1020W press.</p>
                    </div>
                    <div class="card full-height"> <!-- Unit Converter for Endpapers -->
                        <div class="card-header"> <h2 class="text-xl font-semibold" data-translate-key="converterTitle">Unit Converter</h2> </div>
                        <div class="space-y-4 p-2">
                            <div>
                                <label class="block text-sm font-medium dark-mode-text mb-1" data-translate-key="converterWeightTypeLabel">Paper Weight Type</label>
                                <div class="flex space-x-4 mb-2">
                                    <label class="inline-flex items-center"> <input type="radio" class="form-radio h-3.8 w-3.8 text-primary focus:ring-primary-light" name="paperWeightType_endpapers" value="Text" checked> <span class="ml-2 text-sm dark-mode-text" data-translate-key="converterWeightTypeText">Text</span> </label>
                                    <label class="inline-flex items-center"> <input type="radio" class="form-radio h-3.8 w-3.8 text-primary focus:ring-primary-light" name="paperWeightType_endpapers" value="Cover"> <span class="ml-2 text-sm dark-mode-text" data-translate-key="converterWeightTypeCover">Cover</span> </label>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div> <label for="converter_lbs_endpapers" class="form-label" data-translate-key="converterLbsLabel">Pounds (lbs)</label> <div class="relative"> <input type="number" id="converter_lbs_endpapers" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">lbs</span> </div> </div>
                                    <div> <label for="converter_gsm_endpapers" class="form-label" data-translate-key="converterGsmLabel">GSM (g/m²)</label> <div class="relative"> <input type="number" id="converter_gsm_endpapers" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">g/m²</span> </div> </div>
                                </div>
                            </div>
                             <div>
                                <label class="block text-sm font-medium dark-mode-text mb-1" data-translate-key="converterThicknessLabel">Thickness</label>
                                <div class="grid grid-cols-2 gap-3 mb-2">
                                    <div> <label for="converter_pt_endpapers" class="form-label" data-translate-key="converterPtLabel">Points (pt)</label> <div class="relative"> <input type="number" id="converter_pt_endpapers" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">pt</span> </div> </div>
                                    <div> <label for="converter_microns_endpapers" class="form-label" data-translate-key="converterMicronsLabel">Microns (µm)</label> <div class="relative"> <input type="number" id="converter_microns_endpapers" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">µm</span> </div> </div>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div> <label for="converter_in_endpapers" class="form-label" data-translate-key="converterInLabel">Inches (in)</label> <div class="relative"> <input type="number" id="converter_in_endpapers" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">in</span> </div> </div>
                                    <div> <label for="converter_mm_endpapers" class="form-label" data-translate-key="converterMmLabel">Millimeters (mm)</label> <div class="relative"> <input type="number" id="converter_mm_endpapers" class="form-input pr-8" step="any" placeholder="0.00"> <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 dark:text-neutral-400 pointer-events-none">mm</span> </div> </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header collapsible" id="paper-options-header_endpapers">
                        <div> <h2 class="text-xl font-semibold" data-translate-key="paperOptionsTitleEndpapers">Endpaper Paper Options</h2> </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 toggle-icon" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /> </svg>
                    </div>
                    <div class="card-content" id="paper-options-content_endpapers">
                        <p class="text-sm text-neutral-600 mb-3 dark-mode-text" data-translate-key="paperOptionsNote">Enter full original sheet dimensions. Sheets > 720x1020mm will be rotated/trimmed.</p>
                        <div class="table-container">
                            <table id="paper-options-table_endpapers">
                                <thead> <tr> <th class="th-paper-name" data-translate-key="colPaperName">Paper Name</th> <th class="th-source" data-translate-key="colSource">Source</th> <th class="th-sheet-h" data-translate-key="colSheetH">Sheet H (mm)</th> <th class="th-sheet-w" data-translate-key="colSheetW">Sheet W (mm)</th> <th class="th-grain-dir" data-translate-key="colGrainDir">Grain || To</th> <th class="th-caliper" data-translate-key="colCaliper">Caliper (µm)</th> <th class="th-cost-ream" data-translate-key="colCostReam">Cost/Ream ($)</th> <th class="th-gsm" data-translate-key="colGsm">GSM (g/m²)</th> <th class="th-cost-tonne" data-translate-key="colCostTonne">Cost/Ton ($)</th> <th class="th-actions" data-translate-key="colActions">Actions</th> </tr> </thead>
                                <tbody>
                                    <tr data-row-id="1">
                                        <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Woodfree 120gsm"></td>
                                        <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll">Roll</option></select></td>
                                        <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="787.4"></td>
                                        <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="1092.2"></td>
                                        <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>Height</option><option value="Width" data-translate-key="grainWidth">Width</option></select></td>
                                        <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="140" placeholder="µm"></td>
                                        <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="65"></td>
                                        <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="120"></td>
                                        <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value=""></td>
                                        <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td>
                                    </tr>
                                    <tr data-row-id="2">
                                        <td class="td-paper-name"><input type="text" class="form-input paper-name" value="Specialty Endpaper 140gsm"></td>
                                        <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>Pre-Cut</option><option value="Roll" data-translate-key="sourceRoll">Roll</option></select></td>
                                        <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="650"></td>
                                        <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="900"></td>
                                        <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Width" data-translate-key="grainWidth" selected>Width</option><option value="Height" data-translate-key="grainHeight" >Height</option></select></td>
                                        <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="160" placeholder="µm"></td>
                                        <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="80"></td>
                                        <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="140"></td>
                                        <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value=""></td>
                                        <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4 flex justify-between items-center">
                            <button id="add-paper-option_endpapers" class="btn btn-secondary add-paper-option-btn"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /> </svg> <span data-translate-key="addOptionBtn">Add Option</span> </button>
                            <button id="calculate-btn_endpapers" class="btn btn-primary calculate-btn"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00-.076-1.822l-10-4z" clip-rule="evenodd" /></svg> <span data-translate-key="calculateBtnEndpapers">Calculate Endpaper Costs</span> </button>
                        </div>
                    </div>
                </div>
                <div id="results-section_endpapers" class="card hidden results-section-card">
                    <div class="card-header collapsible" id="results-header_endpapers">
                        <div><h2 class="text-xl font-semibold" data-translate-key="resultsTitleEndpapers">Endpaper Results</h2></div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 toggle-icon" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>
                    </div>
                    <div id="results-content_endpapers" class="card-content results-content-card">
                        <div id="results-container_endpapers" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 results-container-div"></div>
                    </div>
                </div>
            </div> <!-- End Endpapers Tab -->
        </div> <!-- End Tab Content Area -->
    </div>

    <!-- Alert Modal -->
    <div id="alert-modal" class="modal-overlay">
        <div class="modal-content"> <p id="modal-message" class="modal-message"></p> <button id="modal-close" class="btn btn-primary modal-close-btn" data-translate-key="modalCloseBtn">OK</button> </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay"> <div class="loading-spinner"></div> </div>

    <!-- Original Selected Components Summary Panel removed from here -->

    <script>
        // --- Translations Object ---
        const translations = {
            en: {
                appTitle: "Multi-Component Paper Cost Estimator",
                appSubtitle: "Estimate costs for Inner Text, Cover, and Endpapers. Max press: 720mm H x 1020mm W.",
                tabInnerText: "Inner Text",
                tabCover: "Cover",
                tabEndpapers: "Endpapers",
                jobSpecsTitle: "Job Specifications",
                pageHeightLabel: "Page Height (mm)", pageHeightLabelIn: "Page Height (in)",
                pageWidthLabel: "Page Width (mm)", pageWidthLabelIn: "Page Width (in)",
                totalPagesLabel: "Total Pages", quantityLabel: "Quantity",
                bindingLabel: "Binding Method", bindingSaddleStitch: "Saddle Stitch", bindingPerfectBound: "Paperback",
                bindingWireO: "Wire-O / Coil", bindingSectionSewn: "Section Sewn", bindingCaseBound: "Case Bound (Hardcover)", bindingSinglePage: "Single Page / Flat Sheet",
                spoilageLabel: "Spoilage (%)",
                prodParamsTitle: "Production Parameters",
                bleedLabel: "Bleed (mm)", gripperLabel: "Gripper (mm)", colorBarLabel: "Color Bar (mm)",
                lipLabel: "Side Lip (mm)", doubleLipTitle: "Double Side Lip",
                alignmentModeLabel: "Grain Alignment", alignmentModeAligned: "Aligned", alignmentModeMisaligned: "Misaligned",
                prodParamsNote: "* Gutter not used for sheet area. Margins applied to 720H x 1020W press.",
                converterTitle: "Unit Converter", converterWeightTypeLabel: "Paper Weight Type", converterWeightTypeText: "Text",
                converterWeightTypeCover: "Cover", converterLbsLabel: "Pounds (lbs)", converterGsmLabel: "GSM (g/m²)",
                converterThicknessLabel: "Thickness", converterPtLabel: "Points (pt)", converterMicronsLabel: "Microns (µm)",
                converterInLabel: "Inches (in)", converterMmLabel: "Millimeters (mm)",
                paperOptionsTitle: "Paper Options",
                paperOptionsNote: "Enter full original sheet dimensions. Sheets > 720x1020mm will be rotated/trimmed.",
                colPaperName: "Paper Name", colSource: "Source", colSheetH: "Sheet H (mm)", colSheetW: "Sheet W (mm)",
                colGrainDir: "Grain || To", colCaliper: "Caliper (µm)", colCostReam: "Cost/Ream ($)",
                colGsm: "GSM (g/m²)", colCostTonne: "Cost/Ton ($)", colActions: "Actions",
                sourcePreCut: "Pre-Cut", sourceRoll: "Roll", grainHeight: "Height", grainWidth: "Width",
                deleteRowTitle: "Delete row", addOptionBtn: "Add Option", calculateBtn: "Calculate Costs",
                resultsTitle: "Calculation Results",
                mostEfficientBadge: "Lowest Waste %", resInputSheet: "Input Sheet (mm)", resPressSize: "Press Size (mm)",
                resNoteRotated: "(Rotated for Press)", resNoteTrimmed: "(Trimmed from {h}H x {w}W)",
                resUsableArea: "Usable Area (mm)", resGrainAlignment: "Grain Alignment",
                grainAligned: "Aligned", grainMisaligned: "Misaligned",
                resUntrimmedPage: "Untrimmed Page (mm)", resImposedArea: "Imposed Area (mm)",
                resLayoutFit: "Layout Fit (Down x Across)", layoutDescSpreadAcross: "({num} spreads across)",
                layoutDescSpreadDown: "({num} spread-rows down)", layoutDescInvalid: "(Invalid layout)", layoutDescNoFit: "(No Fit)",
                resPagesPerSide: "Pages/Side", resSheetUtilization: "Sheet Utilization %", resResultingSig: "Resulting Sig.",
                resTotalSheets: "Total Sheets", resCostPerSheet: "Cost/Sheet", resTotalCost: "Total Cost",
                resErrorPrefix: "Error", resSource: "Source", resInputGrain: "Input Grain", resGsm: "GSM",
                placeholderRollH: "≤ 720", placeholderRollW: "≤ 1020", placeholderMm: "mm", placeholderIn: "in",
                newOptionDefaultName: "New Option {num}",
                alertLeastOneOption: "You must keep at least one paper option.",
                alertInvalidInputs: "Please enter valid positive values for required dimensions, pages, and quantity.",
                alertTotalPagesPositive: "Total pages must be a positive number.",
                alertTotalPagesEven: "Total pages must be an even number.",
                alertNegativeProdParams: "Production parameters (Bleed, Gripper, Color Bar, Side Lip, Caliper, Costs, GSM) cannot be negative.",
                alertNoResults: "No valid paper options entered or calculated. Please check inputs.",
                alertNoResultsForAlignment: "No papers meet the {mode} grain requirement. Try changing the grain alignment.",
                errorInvalidSheetDims: "Invalid sheet dimensions.", errorMissingCostReam: "Missing Cost/Ream for Pre-Cut.",
                errorMissingRollData: "Missing GSM, Cost/Tonne, Height or Width for Roll cost calculation.",
                errorSheetDimsAfterTrim: "Sheet dimensions invalid after trimming.", errorUsableAreaNegative: "Usable area zero/negative {note}",
                errorPageDimsInvalid: "Page dimensions invalid.", errorFitCalc: "Fit calculation error.",
                errorCannotFit: "Cannot fit pages/items.", errorInvalidRollCost: "Invalid Roll cost data.",
                errorCostSheetNegative: "Cost/Sheet is zero/negative.", errorCostCalc: "Final cost calculation error.",
                resNoteHeightInferred: "(H inferred)", resNoteWidthInferred: "(W inferred)",
                rollDimensionsOptimized: "(Roll dims optimized)",
                modalCloseBtn: "OK", na: "N/A",
                optimalOptionsHeader: "Optimal Options", optimalOptionsDescription: "These options achieve the maximum possible fit: {benchmark} pages/items per side.",
                suboptimalOptionsHeader: "Suboptimal Options", suboptimalOptionsDescription: "These options achieve less than the maximum possible fit.",
                errorOptionsHeader: "Error Results", errorOptionsDescription: "These options couldn't be calculated properly.",
                resBookBlockThickness: "Book Block Thickness", resCostPerBook: "Cost Per Book",
                errorMissingCaliper: "Missing Caliper (µm) for thickness calculation.",
                coverSpecsTitle: "Cover Specifications", coverProdParamsTitle: "Cover Prod. Parameters",
                paperOptionsTitleCover: "Cover Paper Options", calculateBtnCover: "Calculate Cover Costs", resultsTitleCover: "Cover Results",
                coverHeightLabel: "Cover Height (mm)", coverHeightLabelIn: "Cover Height (in)",
                coverWidthLabel: "Cover Width (mm)", coverWidthLabelIn: "Cover Width (in)",
                coverDimNote: "Typically same as Inner Text Page dimensions.",
                spineThicknessLabel: "Spine Thickness (mm)", spineThicknessLabelIn: "Spine Thickness (in)",
                coverTypeLabel: "Cover Type",
                coverTypePaperback: "Paperback", coverTypeHardcover: "Hardcover (Case)", coverTypeDustJacket: "Dust Jacket",
                coverQuantityNote: "Matches Inner Text quantity.", coverGrainNote: "Grain typically parallel to spine/cover height.",
                turnInAllowanceLabel: "Turn-in Allowance (mm)", turnInAllowanceNote: "Standard is 15-20mm for hardcover turn-ins.", placeholderTurnInAllowance: "e.g., 18",
                flapWidthLabel: "Flap Width (mm)", flapWidthNote: "Typically half the book width. Auto-calculated by default.", placeholderFlapWidth: "e.g., half book width",
                resItemsPerSide: "Covers/Side", resCostPerCover: "Cost Per Cover", errorCoverSpreadSize: "Invalid cover spread size calculation.",
                endpapersSpecsTitle: "Endpaper Specifications", endpaperProdParamsTitle: "Endpaper Prod. Parameters",
                paperOptionsTitleEndpapers: "Endpaper Paper Options", calculateBtnEndpapers: "Calculate Endpaper Costs", resultsTitleEndpapers: "Endpaper Results",
                endpaperHeightLabel: "Endpaper Height (mm)", endpaperHeightLabelIn: "Endpaper Height (in)",
                endpaperWidthLabel: "Endpaper Width (mm)", endpaperWidthLabelIn: "Endpaper Width (in)",
                endpaperTypeLabel: "Endpaper Type", endpaperTypeSingle: "Single Leaf (4pp per book)", endpaperTypeDouble: "Double Leaf (8pp per book)",
                resLeavesPerSide: "Leaves/Side", resCostPerEndpaperSet: "Cost Per Endpaper Set", errorEndpaperSpreadSize: "Invalid endpaper spread size calculation.",
                summaryTitle: "Selected Components", summaryNoSelection: "No components selected yet.",
                summaryTotalCostPerBook: "Total Cost/Book:", summaryInnerText: "Inner Text",
                summaryCover: "Cover", summaryEndpapers: "Endpapers",
                summaryPaperDetails: "Paper Details", summaryJobSpecs: "Job Specs",
                summarySource: "Source", summaryGrain: "Grain", summaryGsm: "GSM", summaryCaliper: "Caliper",
                summaryPageSize: "Size", summaryPageCount: "Page Count", summaryQuantity: "Quantity",
                summaryCoverType: "Cover Type", summaryEndpaperType: "Endpaper Type",
                selectThisOption: "Select this Option", optionSelected: "Selected",
                removeItemTitle: "Remove item",
                // Placeholders - English
                placeholderPageHeight: "e.g., 225", placeholderPageHeightIn: "e.g., 8.86",
                placeholderPageWidth: "e.g., 150", placeholderPageWidthIn: "e.g., 5.91",
                placeholderTotalPages: "e.g., 320", placeholderQuantity: "e.g., 1000",
                placeholderSpoilage: "e.g., 5", placeholderBleed: "e.g., 3",
                placeholderGripper: "e.g., 12", placeholderColorBar: "e.g., 6",
                placeholderLip: "e.g., 5",
                placeholderCoverHeight: "e.g., 225", placeholderCoverHeightIn: "e.g., 8.86",
                placeholderCoverWidth: "e.g., 150", placeholderCoverWidthIn: "e.g., 5.91",
                placeholderSpineThickness: "e.g., 15 or auto", placeholderSpineThicknessIn: "e.g., 0.59 or auto",

                placeholderEndpaperHeight: "e.g., 225", placeholderEndpaperHeightIn: "e.g., 8.86",
                placeholderEndpaperWidth: "e.g., 150", placeholderEndpaperWidthIn: "e.g., 5.91",
            },
            zh: { // Ensure your Chinese translations are complete for all new keys
                appTitle: "多部件纸张成本估算器",
                appSubtitle: "估算内文、封面和衬纸的成本。最大印刷机尺寸：720毫米高 x 1020毫米宽。",
                tabInnerText: "内文", tabCover: "封面", tabEndpapers: "衬纸",
                jobSpecsTitle: "产品规格", pageHeightLabel: "页面高度 (毫米)", pageHeightLabelIn: "页面高度 (英寸)",
                pageWidthLabel: "页面宽度 (毫米)", pageWidthLabelIn: "页面宽度 (英寸)",
                totalPagesLabel: "总页数", quantityLabel: "数量",
                bindingLabel: "装订方式", bindingSaddleStitch: "骑马钉", bindingPerfectBound: "平装",
                bindingWireO: "蛇仔/YO", bindingSectionSewn: "穿线平装", bindingCaseBound: "精装", bindingSinglePage: "单页/平张",
                spoilageLabel: "损耗率 (%)", prodParamsTitle: "生产参数",
                bleedLabel: "出血 (毫米)", gripperLabel: "牙口 (毫米)", colorBarLabel: "色彩条 (毫米)",
                lipLabel: "漂口 (毫米)", doubleLipTitle: "双漂口",
                alignmentModeLabel: "产品顺纹", alignmentModeAligned: "顺纹", alignmentModeMisaligned: "错纹",
                prodParamsNote: "*边距基于720毫米高 x 1020毫米宽的印刷机限制。",
                converterTitle: "单位换算器", converterWeightTypeLabel: "纸张重量类型", converterWeightTypeText: "内文纸",
                converterWeightTypeCover: "封面纸", converterLbsLabel: "磅 (lbs)", converterGsmLabel: "克重 (g/m²)",
                converterThicknessLabel: "厚度", converterPtLabel: "点 (pt)", converterMicronsLabel: "微米 (µm)",
                converterInLabel: "英寸 (in)", converterMmLabel: "毫米 (mm)",
                paperOptionsTitle: "纸张选项", paperOptionsNote: "输入完整原始纸张尺寸。超过720x1020毫米的纸张将被旋转/切除。",
                colPaperName: "纸张名称", colSource: "来源", colSheetH: "纸高 (毫米)", colSheetW: "纸宽 (毫米)",
                colGrainDir: "纸纹平行于", colCaliper: "厚度 (微米)", colCostReam: "成本/令 ($)",
                colGsm: "克重 (克/平方米)", colCostTonne: "$/吨 ", colActions: "操作",
                sourcePreCut: "预切纸", sourceRoll: "卷筒纸", grainHeight: "高", grainWidth: "宽",
                deleteRowTitle: "删除行", addOptionBtn: "新增", calculateBtn: "计算",
                resultsTitle: "计算结果", mostEfficientBadge: "最优解", resInputSheet: "输入纸张 (毫米)", resPressSize: "上车尺寸 (毫米)",
                resNoteRotated: "(90度旋转)", resNoteTrimmed: "(由 {h}高 x {w}宽 裁剪)",
                resUsableArea: "可用面积 (毫米)", resGrainAlignment: "成品纸纹", grainAligned: "顺纹", grainMisaligned: "错纹",
                resUntrimmedPage: "未切页面 (毫米)", resImposedArea: "拼版面积 (毫米)",
                resLayoutFit: "布局(纵 x 横)", layoutDescSpreadAcross: "({num} 横向跨页)",
                layoutDescSpreadDown: "({num} 纵向跨行)", layoutDescInvalid: "(无效布局)", layoutDescNoFit: "(不适合)",
                resPagesPerSide: "每面页数", resSheetUtilization: "纸张使用率 %", resResultingSig: "成品帖数",
                resTotalSheets: "总张数", resCostPerSheet: "每张成本", resTotalCost: "总成本",
                resErrorPrefix: "错误", resSource: "来源", resInputGrain: "输入纸纹", resGsm: "克重",
                placeholderRollH: "≤ 720", placeholderRollW: "≤ 1020", placeholderMm: "毫米", placeholderIn: "英寸",
                newOptionDefaultName: "新纸 {num}",
                alertLeastOneOption: "您必须至少保留一个纸张选项。",
                alertInvalidInputs: "请输入有效的正数用于所需尺寸、页数和数量。",
                alertTotalPagesPositive: "总页数必须是正数。",
                alertTotalPagesEven: "总页数必须是偶数。",
                alertNegativeProdParams: "生产参数（出血、牙口、色标、漂口、厚度、成本、克重）不能为负数。",
                alertNoResults: "未输入或计算出有效的纸张选项。请检查输入。",
                alertNoResultsForAlignment: "没有纸张符合 {mode} 纸纹要求。尝试更改纸纹对齐方式。",
                errorInvalidSheetDims: "无效的纸张尺寸。", errorMissingCostReam: "预切纸缺少成本/令。",
                errorMissingRollData: "卷筒纸成本计算缺少克重、成本/吨、高度或宽度。",
                errorSheetDimsAfterTrim: "修剪后纸张尺寸无效。", errorUsableAreaNegative: "可用面积为零/负数 {note}",
                errorPageDimsInvalid: "页面尺寸无效。", errorFitCalc: "适合度计算错误。",
                errorCannotFit: "无法容纳页面/物件。", errorInvalidRollCost: "无效的卷筒纸成本数据。",
                errorCostSheetNegative: "每张成本为零/负数。", errorCostCalc: "最终成本计算错误。",
                resNoteHeightInferred: "(纸张高度根据排版布局推断)", resNoteWidthInferred: "(纸张宽度根据排版布局推断)",
                rollDimensionsOptimized: "(宽高已优化)",
                modalCloseBtn: "确定", na: "不适用",
                optimalOptionsHeader: "最佳选项", optimalOptionsDescription: "可实现最优排版布局: 每面排 {benchmark} 页。",
                suboptimalOptionsHeader: "次优选项", suboptimalOptionsDescription: "未能达到最佳排版佈局",
                errorOptionsHeader: "错误结果", errorOptionsDescription: "选项无法计算。",
                resBookBlockThickness: "内文厚度", resCostPerBook: "单册成本",
                errorMissingCaliper: "缺少厚度 (µm) 无法计算内文厚度。",
                coverSpecsTitle: "封面规格", coverProdParamsTitle: "封面生产参数",
                paperOptionsTitleCover: "封面纸张选项", calculateBtnCover: "计算封面", resultsTitleCover: "封面结果",
                coverHeightLabel: "封面高度 (毫米)", coverHeightLabelIn: "封面高度 (英寸)",
                coverWidthLabel: "封面宽度 (毫米)", coverWidthLabelIn: "封面宽度 (英寸)",
                coverDimNote: "通常与内文页面尺寸相同。",
                spineThicknessLabel: "书脊厚度 (毫米)", spineThicknessLabelIn: "书脊厚度 (英寸)",
                coverTypeLabel: "封面类型",
                coverTypePaperback: "平装", coverTypeHardcover: "精装", coverTypeDustJacket: "书套",
                coverQuantityNote: "与内文数量一致。", coverGrainNote: "纸纹通常平行于书脊/封面高度。",
                turnInAllowanceLabel: "包边余量 (毫米)", turnInAllowanceNote: "精装包边通常为15-20毫米。", placeholderTurnInAllowance: "例如, 18",
                flapWidthLabel: "翼仔边宽 (毫米)", flapWidthNote: "默认为书本宽度的一半。", placeholderFlapWidth: "例如，书本宽度的一半",
                resItemsPerSide: "封面数/面", resCostPerCover: "单封面成本", errorCoverSpreadSize: "无效的封面展开尺寸计算。",
                endpapersSpecsTitle: "衬纸规格", endpaperProdParamsTitle: "衬纸生产参数",
                paperOptionsTitleEndpapers: "衬纸纸张选项", calculateBtnEndpapers: "计算衬纸", resultsTitleEndpapers: "衬纸结果",
                endpaperHeightLabel: "衬纸高度 (毫米)", endpaperHeightLabelIn: "衬纸高度 (英寸)",
                endpaperWidthLabel: "衬纸宽度 (毫米)", endpaperWidthLabelIn: "衬纸宽度 (英寸)",
                endpaperTypeLabel: "衬纸类型", endpaperTypeSingle: "单衬（每册4面）", endpaperTypeDouble: "双衬（每册8面）",
                resLeavesPerSide: "衬纸张/面", resCostPerEndpaperSet: "每套衬纸成本", errorEndpaperSpreadSize: "无效的衬纸展开尺寸计算。",
                summaryTitle: "已选组件", summaryNoSelection: "尚未选择任何组件。",
                summaryTotalCostPerBook: "单册总成本:", summaryInnerText: "内文",
                summaryCover: "封面", summaryEndpapers: "衬纸",
                summaryPaperDetails: "纸张详情", summaryJobSpecs: "产品规格",
                summarySource: "来源", summaryGrain: "纸纹", summaryGsm: "克重", summaryCaliper: "厚度",
                summaryPageSize: "尺寸", summaryPageCount: "页数", summaryQuantity: "数量",
                summaryCoverType: "封面类型", summaryEndpaperType: "衬纸类型",
                selectThisOption: "选择此选项", optionSelected: "已选择",
                removeItemTitle: "移除选项",
                // Placeholders - Chinese
                placeholderPageHeight: "例如, 225", placeholderPageHeightIn: "例如, 8.86",
                placeholderPageWidth: "例如, 150", placeholderPageWidthIn: "例如, 5.91",
                placeholderTotalPages: "例如, 320", placeholderQuantity: "例如, 1000",
                placeholderSpoilage: "例如, 5", placeholderBleed: "例如, 3",
                placeholderGripper: "例如, 12", placeholderColorBar: "例如, 6",
                placeholderLip: "例如, 5",
                placeholderCoverHeight: "例如, 225", placeholderCoverHeightIn: "例如, 8.86",
                placeholderCoverWidth: "例如, 150", placeholderCoverWidthIn: "例如, 5.91",
                placeholderSpineThickness: "例如, 15 或自动", placeholderSpineThicknessIn: "例如, 0.59 或自动",

                placeholderEndpaperHeight: "例如, 225", placeholderEndpaperHeightIn: "例如, 8.86",
                placeholderEndpaperWidth: "例如, 150", placeholderEndpaperWidthIn: "例如, 5.91",
            }
        };

        let currentLanguage = 'en';
        let currentTab = 'innerText';
        // Holds arrays of selected paper option objects for each component type.
        // Each object will contain all relevant data from the calculation result,
        // including its unique paper ID and the jobSpecs active at the time of its selection.
        let selectedComponents = { innerText: [], cover: [], endpapers: [] };
        let nextRowIdCounters = { innerText: 9, cover: 3, endpapers: 3 }; // Start after default rows for each


        document.addEventListener('DOMContentLoaded', function() {
            const languageSwitcher = document.getElementById('language-switcher');
            const themeSwitcher = document.getElementById('theme-switcher');
            const alertModal = document.getElementById('alert-modal');
            const modalMessage = document.getElementById('modal-message');
            const modalCloseBtn = document.getElementById('modal-close');
            const loadingOverlay = document.getElementById('loading-overlay');
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            const summaryToggleButton = document.getElementById('summary-toggle-button');
            const summaryPanel = document.getElementById('summary-panel');
            const summaryContentEl = document.getElementById('summary-content');
            const summaryTotalCostEl = document.getElementById('summary-total-cost');

            const safeParseFloat = (v, d = 0) => { const p = parseFloat(v); return isNaN(p) ? d : p; };
            const safeParseInt = (v, d = 0) => { const p = parseInt(v, 10); return isNaN(p) ? d : p; };
            const floor = Math.floor; const ceil = Math.ceil;
            const mmToIn = (mm) => (mm / 25.4); const inToMm = (inch) => (inch * 25.4);

            function initializeCollapsibleCards() {
                document.querySelectorAll('.card-header.collapsible').forEach(header => {
                    const content = header.nextElementSibling;
                    const icon = header.querySelector('.toggle-icon');
                    if (!content || !icon) return;

                    const parentTabContent = header.closest('.tab-content');
                    const isCardInActiveTab = parentTabContent ? parentTabContent.classList.contains('active') : true;

                    // Respect user's manual collapse state
                    if (content.classList.contains('user-collapsed')) {
                        content.classList.add('collapsed');
                        icon.classList.add('collapsed');
                        content.style.maxHeight = '0';
                    } else if (isCardInActiveTab) { // If in active tab and not user-collapsed, expand it
                        content.classList.remove('collapsed');
                        icon.classList.remove('collapsed');
                        requestAnimationFrame(() => {
                            content.style.maxHeight = content.scrollHeight + 'px';
                        });
                    } else { // Inactive tab, not user-collapsed: prepare for expansion when tab activates
                        content.classList.remove('collapsed');
                        icon.classList.remove('collapsed');
                        // MaxHeight will be set when tab becomes active
                    }

                    if (!header.dataset.listenerAttached) {
                        header.addEventListener('click', function() {
                            content.classList.toggle('collapsed');
                            icon.classList.toggle('collapsed');
                            if (content.classList.contains('collapsed')) {
                                content.classList.add('user-collapsed'); // Mark as user action
                                content.style.maxHeight = '0';
                            } else {
                                content.classList.remove('user-collapsed');
                                content.style.maxHeight = content.scrollHeight + 'px';
                            }
                        });
                        header.dataset.listenerAttached = 'true';
                    }
                });
            }

            function updateCollapsibleMaxHeight(contentElement) {
                if (contentElement && !contentElement.classList.contains('collapsed') && !contentElement.classList.contains('user-collapsed')) {
                    requestAnimationFrame(() => { // Ensure DOM update before measuring
                         contentElement.style.maxHeight = contentElement.scrollHeight + 'px';
                    });
                }
            }

            function showAlert(key, replacements = {}) {
                let message = translations[currentLanguage][key] || key;
                for (const k in replacements) { message = message.replace(`{${k}}`, replacements[k]); }
                modalMessage.textContent = message;
                alertModal.classList.add('visible');
            }
            modalCloseBtn.addEventListener('click', () => alertModal.classList.remove('visible'));
            alertModal.addEventListener('click', (e) => { if (e.target === alertModal) alertModal.classList.remove('visible'); });

            function switchTheme(theme) {
                if (theme === 'dark') { document.body.classList.add('dark-mode'); themeSwitcher.classList.add('dark'); }
                else { document.body.classList.remove('dark-mode'); themeSwitcher.classList.remove('dark'); }
                localStorage.setItem('preferredTheme', theme);
            }
            let preferredTheme = localStorage.getItem('preferredTheme') || 'light';
            switchTheme(preferredTheme);
            themeSwitcher.addEventListener('click', () => { preferredTheme = document.body.classList.contains('dark-mode') ? 'light' : 'dark'; switchTheme(preferredTheme); });

            function initializeSwitches(tabId) {
                const alignmentSwitcher = document.getElementById(`alignment-mode-switcher_${tabId}`);
                if (alignmentSwitcher && !alignmentSwitcher.dataset.listenerAttached) {
                    alignmentSwitcher.classList.remove('misaligned'); // Default to Aligned
                    alignmentSwitcher.addEventListener('click', function() { this.classList.toggle('misaligned'); });
                    alignmentSwitcher.dataset.listenerAttached = 'true';
                }
                const dblLipSwitcher = document.getElementById(`double-lip-switcher_${tabId}`);
                if (dblLipSwitcher && !dblLipSwitcher.dataset.listenerAttached) {
                    dblLipSwitcher.classList.remove('active'); // Default to inactive (x1)
                    dblLipSwitcher.addEventListener('click', function() { this.classList.toggle('active'); });
                    dblLipSwitcher.dataset.listenerAttached = 'true';
                }
            }

            function switchLanguage(lang) {
                if (!translations[lang]) return;
                currentLanguage = lang; document.documentElement.lang = lang;
                if (lang === 'zh') languageSwitcher.classList.add('active'); else languageSwitcher.classList.remove('active');

                // First, collect all elements that need translation
                const elementsToTranslate = document.querySelectorAll('[data-translate-key]');

                // Apply animations and translations consistently to all elements
                elementsToTranslate.forEach(el => {
                    const key = el.dataset.translateKey;
                    const translation = translations[lang][key];

                    // Apply animation only to visible text elements, not to inputs or attributes
                    const shouldAnimate = !(el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' ||
                                          (el.hasAttribute('title') && !el.textContent.trim()));

                    if (shouldAnimate) {
                        el.classList.remove('text-transition-in');
                        el.classList.add('text-transition-out');
                    }

                    setTimeout(() => {
                        if (translation !== undefined) {
                            if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA') {
                                if (el.type === 'button' || el.type === 'submit') el.value = translation;
                            }
                            else if (el.tagName === 'OPTION') el.textContent = translation;
                            else if (el.hasAttribute('title') && (key.toLowerCase().includes('title') || key === 'deleteRowTitle')) el.title = translation;
                            else {
                                let target = el;
                                const span = el.querySelector('span:not([class*="switch"])');
                                if (span && el.childNodes.length > 1) target = span;
                                target.textContent = translation;
                            }
                        }

                        if (shouldAnimate) {
                            el.classList.remove('text-transition-out');
                            el.classList.add('text-transition-in');
                            el.addEventListener('animationend', () => el.classList.remove('text-transition-in'), { once: true });
                        }
                    }, 120);
                });

                // Apply translated placeholders without animation
                document.querySelectorAll('[data-placeholder-translate-key]').forEach(el => {
                    const key = el.dataset.placeholderTranslateKey;
                    const translation = translations[lang][key];
                    if (translation !== undefined) {
                        el.placeholder = translation;
                    }
                });

                // Apply translated title attributes for tooltips
                document.querySelectorAll('[data-title-translate-key]').forEach(el => {
                    const key = el.dataset.titleTranslateKey;
                    const translation = translations[lang][key];
                    if (translation !== undefined) {
                        el.title = translation;
                    }
                });

                // Handle paper options consistently without animations for input values
                ['innerText', 'cover', 'endpapers'].forEach(tabId => {
                    document.querySelectorAll(`#paper-options-table_${tabId} tbody tr`).forEach(row => {
                        toggleRollPlaceholders(row, tabId);
                        const nameInput = row.querySelector('.paper-name');
                        if (nameInput) {
                            const defEn = translations.en.newOptionDefaultName.replace('{num}', row.dataset.rowId);
                            const defZh = translations.zh.newOptionDefaultName.replace('{num}', row.dataset.rowId);
                            if (nameInput.value === defEn || nameInput.value === defZh) {
                                nameInput.value = translations[lang].newOptionDefaultName.replace('{num}', row.dataset.rowId);
                            }
                        }
                    });
                });
                const resultsSectionForCurrentTab = document.getElementById(`results-section_${currentTab}`);
                if (resultsSectionForCurrentTab && resultsSectionForCurrentTab.dataset.resultsData) {
                    const data = JSON.parse(resultsSectionForCurrentTab.dataset.resultsData);
                    const bench = parseFloat(resultsSectionForCurrentTab.dataset.benchmark || 0);
                    displayResults(data, document.getElementById(`results-container_${currentTab}`), currentTab, bench);
                }
                updateSummaryPanel();
            }
            languageSwitcher.addEventListener('click', () => { const newLang = currentLanguage === 'en' ? 'zh' : 'en'; switchLanguage(newLang); });

            function setupUnitConverterGroup(mmId, inId) {
                const mmInput = document.getElementById(mmId), inchInput = document.getElementById(inId);
                let updating = false; if (!mmInput || !inchInput) return;
                mmInput.addEventListener('input', () => {
                    if (updating) return; const mm = safeParseFloat(mmInput.value, -1); updating = true;
                    if (mm >= 0) inchInput.value = mmToIn(mm).toFixed(3); else if (mmInput.value.trim() === '') inchInput.value = '';
                    updating = false;
                });
                inchInput.addEventListener('input', () => {
                    if (updating) return; const inches = safeParseFloat(inchInput.value, -1); updating = true;
                    if (inches >= 0) mmInput.value = inToMm(inches).toFixed(1); else if (inchInput.value.trim() === '') mmInput.value = '';
                    updating = false;
                });
            }
            function setupStandaloneConverter(tabId) {
                let updating = false;
                const lbs = document.getElementById(`converter_lbs_${tabId}`), gsm = document.getElementById(`converter_gsm_${tabId}`),
                      pt = document.getElementById(`converter_pt_${tabId}`), microns = document.getElementById(`converter_microns_${tabId}`),
                      inch = document.getElementById(`converter_in_${tabId}`), mm = document.getElementById(`converter_mm_${tabId}`),
                      radios = document.querySelectorAll(`input[name="paperWeightType_${tabId}"]`);
                const FACTORS = { text: 1.48, cover: 2.70, pt_microns: (25.4/72)*1000, in_mm: 25.4 };
                function convert(srcEl) {
                    if (updating || !srcEl) return; const id = srcEl.id, val = safeParseFloat(srcEl.value, -1); updating = true;
                    try {
                        let targetVal = '', targetEl = null;
                        if (val >= 0) {
                            if (id.includes('_lbs') || id.includes('_gsm')) {
                                const type = document.querySelector(`input[name="paperWeightType_${tabId}"]:checked`)?.value;
                                let factor = type === 'Text' ? FACTORS.text : FACTORS.cover; // Default to cover if type not found
                                if (!type) factor = FACTORS.cover; // Fallback
                                if (id.includes('_lbs')) { targetVal = (val * factor).toFixed(1); targetEl = gsm; }
                                else { targetVal = (val / factor).toFixed(1); targetEl = lbs; }
                            } else if (id.includes('_pt')) { targetVal = (val * FACTORS.pt_microns).toFixed(1); targetEl = microns; }
                            else if (id.includes('_microns')) { targetVal = (val / FACTORS.pt_microns).toFixed(2); targetEl = pt; }
                            else if (id.includes('_in')) { targetVal = (val * FACTORS.in_mm).toFixed(1); targetEl = mm; }
                            else if (id.includes('_mm')) { targetVal = (val / FACTORS.in_mm).toFixed(3); targetEl = inch; }
                            if (targetEl) targetEl.value = targetVal;
                        } else if (srcEl.value.trim() === '') {
                            if (id.includes('_lbs') && gsm) gsm.value = ''; else if (id.includes('_gsm') && lbs) lbs.value = '';
                            else if (id.includes('_pt') && microns) microns.value = ''; else if (id.includes('_microns') && pt) pt.value = '';
                            else if (id.includes('_in') && mm) mm.value = ''; else if (id.includes('_mm') && inch) inch.value = '';
                        }
                    } catch(e) { console.error("Conv err:", e); if(targetEl) targetEl.value = '';}
                    finally { updating = false; }
                }
                [lbs, gsm, pt, microns, inch, mm].forEach(inp => { if (inp && !inp.dataset.convListener) { inp.addEventListener('input', (e)=>convert(e.target)); inp.dataset.convListener = 'true';}});
                radios.forEach(rad => { if(!rad.dataset.convListener) { rad.addEventListener('change', ()=>{ if(lbs?.value) convert(lbs); else if(gsm?.value) convert(gsm);}); rad.dataset.convListener = 'true';}});
            }

            function showLoadingOverlay() { if (loadingOverlay) loadingOverlay.classList.add('visible'); }
            function hideLoadingOverlay() { if (loadingOverlay) loadingOverlay.classList.remove('visible'); }

            function toggleRollPlaceholders(row, tabId) {
                const sourceSelect = row.querySelector('.source');
                const sheetHInput = row.querySelector('.sheet-h');
                const sheetWInput = row.querySelector('.sheet-w');
                const caliperInput = row.querySelector('.caliper-microns');
                const costReamInput = row.querySelector('.cost-ream');
                const gsmInput = row.querySelector('.gsm');
                const costTonneInput = row.querySelector('.cost-tonne');

                if (sourceSelect && sheetHInput && sheetWInput) {
                    // Apply sheet dimension placeholders regardless of source type
                    if (!sheetHInput.value) {
                        sheetHInput.placeholder = "≤ 720";
                    }
                    if (!sheetWInput.value) {
                        sheetWInput.placeholder = "≤ 1020";
                    }

                    // Add placeholders for other fields regardless of source
                    if (caliperInput) {
                        caliperInput.placeholder = "µm";
                    }
                    if (costReamInput) {
                        costReamInput.placeholder = "$";
                    }
                    if (gsmInput) {
                        gsmInput.placeholder = "g/m²";
                    }
                    if (costTonneInput) {
                        costTonneInput.placeholder = "$";
                    }
                }
            }
            function addPaperOption(tabId) {
                const tableBody = document.getElementById(`paper-options-table_${tabId}`).querySelector('tbody');
                const newRow = document.createElement('tr');
                const newId = nextRowIdCounters[tabId]++; newRow.dataset.rowId = newId;
                const defaultName = translations[currentLanguage].newOptionDefaultName.replace('{num}', newId);
                const placeholderCaliper = translations[currentLanguage].placeholderCaliper || "µm";
                // Set placeholders for sheet dimensions - same for both Pre-Cut and Roll
                const placeholderH = '≤ 720';
                const placeholderW = '≤ 1020';

                newRow.innerHTML = `
                    <td class="td-paper-name"><input type="text" class="form-input paper-name" value="${defaultName}"></td>
                    <td class="td-source"><select class="form-select source"><option value="Pre-Cut" data-translate-key="sourcePreCut" selected>${translations[currentLanguage].sourcePreCut}</option><option value="Roll" data-translate-key="sourceRoll">${translations[currentLanguage].sourceRoll}</option></select></td>
                    <td class="td-sheet-h"><input type="number" step="0.1" class="form-input sheet-h" value="" placeholder="${placeholderH}"></td>
                    <td class="td-sheet-w"><input type="number" step="0.1" class="form-input sheet-w" value="" placeholder="${placeholderW}"></td>
                    <td class="td-grain-dir"><select class="form-select grain-dir"><option value="Height" data-translate-key="grainHeight" selected>${translations[currentLanguage].grainHeight}</option><option value="Width" data-translate-key="grainWidth">${translations[currentLanguage].grainWidth}</option></select></td>
                    <td class="td-caliper"><input type="number" step="1" class="form-input caliper-microns" value="" placeholder="µm"></td>
                    <td class="td-cost-ream"><input type="number" step="0.01" class="form-input cost-ream" value="" placeholder="$"></td>
                    <td class="td-gsm"><input type="number" step="1" class="form-input gsm" value="" placeholder="g/m²"></td>
                    <td class="td-cost-tonne"><input type="number" step="0.01" class="form-input cost-tonne" value="" placeholder="$"></td>
                    <td class="td-actions"><button class="delete-row" data-translate-key="deleteRowTitle" title="${translations[currentLanguage].deleteRowTitle}"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg></button></td>
                `;
                tableBody.appendChild(newRow);
                newRow.querySelector('.source').addEventListener('change', function() { toggleRollPlaceholders(newRow, tabId); });
                newRow.querySelector('.sheet-h').addEventListener('input', function() { toggleRollPlaceholders(newRow, tabId); });
                newRow.querySelector('.sheet-w').addEventListener('input', function() { toggleRollPlaceholders(newRow, tabId); });
                toggleRollPlaceholders(newRow, tabId); // Initial placeholder state
                updateCollapsibleMaxHeight(document.getElementById(`paper-options-content_${tabId}`));
            }
            function deleteRow(rowEl, tabId) {
                const tableBody = document.getElementById(`paper-options-table_${tabId}`).querySelector('tbody');
                if (tableBody.querySelectorAll('tr').length > 1) {
                    rowEl.remove();
                    updateCollapsibleMaxHeight(document.getElementById(`paper-options-content_${tabId}`));
                } else showAlert('alertLeastOneOption');
            }

            summaryToggleButton.addEventListener('click', () => {
                // Use requestAnimationFrame to optimize the toggle animation
                requestAnimationFrame(() => {
                    summaryPanel.classList.toggle('visible');
                });
            });
            function updateSummaryPanel() {
                // Use requestAnimationFrame to optimize rendering
                requestAnimationFrame(() => {
                    const T = translations[currentLanguage];
                    let content = '';
                    let totalCost = 0;
                    let hasSel = false;
                    const na = T.na || 'N/A';
                    const formatMmIn = (mm) => mm && !isNaN(mm) ? `${mm.toFixed(1)}mm (${mmToIn(mm).toFixed(2)}")` : na;

                    function renderComponent(compType, labelKey, costLabelKey, data) {
                        if (!data) return '';
                        hasSel = true; // Mark that we have at least one selection
                        // Use the correct cost property based on component type
                        const costValue = data.costPerBook || 0; // costPerBook holds the unit cost for all types now
                        totalCost += costValue;

                        const sourceText = data.source === 'Pre-Cut' ? T.sourcePreCut : T.sourceRoll;
                        const grainText = data.grainDirInput === 'Height' ? T.grainHeight : T.grainWidth;
                        const gsmText = data.gsm && !isNaN(data.gsm) ? `${data.gsm} g/m²` : na;
                        const caliperText = data.caliperMicrons && !isNaN(data.caliperMicrons) ? `${data.caliperMicrons.toFixed(0)} µm` : na;

                        // Format page size display
                        let pageSizeText = na;
                        if (data.jobSpecs?.trimH && !isNaN(data.jobSpecs.trimH) && data.jobSpecs?.trimW && !isNaN(data.jobSpecs.trimW)) {
                            const mmText = `${data.jobSpecs.trimH.toFixed(1)} × ${data.jobSpecs.trimW.toFixed(1)}`;
                            const inchH = mmToIn(data.jobSpecs.trimH).toFixed(2);
                            const inchW = mmToIn(data.jobSpecs.trimW).toFixed(2);
                            pageSizeText = `${mmText}<br><span class="text-neutral-500 dark:text-neutral-400 pl-1">(${inchH}" × ${inchW}")</span>`;
                        }

                        const quantityText = data.jobSpecs?.quantity?.toLocaleString() || na;

                        let specificSpecHTML = '';
                        let bindingText = ''; // For inner text binding method

                        if (compType === 'innerText') {
                            bindingText = T['binding' + data.jobSpecs?.bindingMethod?.charAt(0).toUpperCase() + data.jobSpecs?.bindingMethod?.slice(1)] || na;
                            specificSpecHTML = `
                                <div class="text-xs"><span class="font-medium">${T.summaryPageCount}:</span> ${data.jobSpecs?.totalPages || na}</div>
                                <div class="text-xs"><span class="font-medium">${T.bindingLabel}:</span> ${bindingText}</div>`;
                        } else if (compType === 'cover') {
                            const coverTypeText = T['coverType' + data.jobSpecs?.coverType?.charAt(0).toUpperCase() + data.jobSpecs?.coverType?.slice(1)] || na;
                            specificSpecHTML = `<div class="text-xs"><span class="font-medium">${T.summaryCoverType}:</span> ${coverTypeText}</div>`;
                        } else if (compType === 'endpapers') {
                            const endpaperTypeText = T['endpaperType' + data.jobSpecs?.endpaperType?.charAt(0).toUpperCase() + data.jobSpecs?.endpaperType?.slice(1)] || na;
                            specificSpecHTML = `<div class="text-xs"><span class="font-medium">${T.summaryEndpaperType}:</span> ${endpaperTypeText}</div>`;
                        }

                        return `
                            <div class="summary-item" data-summary-item-id="${data.id}" data-summary-component-type="${compType}">
                                <div class="flex justify-between items-start">
                                    <div class="summary-item-label font-semibold text-base mb-1">${T[labelKey]}</div>
                                    <button class="remove-summary-item text-neutral-500 hover:text-danger p-1 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors" title="${T.removeItemTitle || 'Remove item'}">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                <!-- User Input Specs -->
                                <div class="summary-item-label text-xs font-semibold mt-1 mb-0.5">${T.summaryJobSpecs}</div>
                                <div class="grid grid-cols-2 gap-x-2 gap-y-1 text-xs mb-2">
                                    <div><span class="font-medium">${T.summaryPageSize}:</span> ${pageSizeText}</div>
                                    <div><span class="font-medium">${T.summaryQuantity}:</span> ${quantityText}</div>
                                    <div class="col-span-2">${specificSpecHTML}</div>
                                </div>

                                <!-- Selected Paper Details -->
                                <div class="summary-item-label text-xs font-semibold mt-2 mb-0.5">${T.summaryPaperDetails}</div>
                                <div class="summary-item-value font-medium mb-1 text-sm">${data.paperName}</div>
                                <div class="grid grid-cols-2 gap-x-2 gap-y-1 text-xs mb-2">
                                    <div><span class="font-medium">${T.summarySource}:</span> ${sourceText}</div>
                                    <div><span class="font-medium">${T.summaryGrain}:</span> ${grainText}</div>
                                    <div><span class="font-medium">${T.summaryGsm}:</span> ${gsmText}</div>
                                    <div><span class="font-medium">${T.summaryCaliper}:</span> ${caliperText}</div>
                                </div>

                                <!-- Cost -->
                                <div class="summary-item-value text-sm font-semibold mt-1">${T[costLabelKey]}: $${costValue.toFixed(2)}</div>
                            </div>`;
                    }

                    // Iterate through each array of selected components
                    selectedComponents.innerText.forEach(item => {
                        content += renderComponent('innerText', 'summaryInnerText', 'resCostPerBook', item);
                    });

                    selectedComponents.cover.forEach(item => {
                        content += renderComponent('cover', 'summaryCover', 'resCostPerCover', item);
                    });

                    selectedComponents.endpapers.forEach(item => {
                        content += renderComponent('endpapers', 'summaryEndpapers', 'resCostPerEndpaperSet', item);
                    });

                    if (!hasSel) content = `<p class="text-sm text-neutral-500 dark-mode-text" data-translate-key="summaryNoSelection">${T.summaryNoSelection}</p>`;

                    // Use a DocumentFragment for better performance
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = content;

                    // Clear and append in a single operation
                    while (summaryContentEl.firstChild) {
                        summaryContentEl.removeChild(summaryContentEl.firstChild);
                    }

                    // Use a document fragment to minimize reflows
                    const fragment = document.createDocumentFragment();
                    while (tempDiv.firstChild) {
                        fragment.appendChild(tempDiv.firstChild);
                    }

                    summaryContentEl.appendChild(fragment);
                    summaryTotalCostEl.textContent = `$${totalCost.toFixed(2)}`;

                    // Add event listeners to the remove buttons
                    summaryContentEl.querySelectorAll('.remove-summary-item').forEach(btn => {
                        btn.addEventListener('click', function(e) {
                            const summaryItem = e.currentTarget.closest('.summary-item');
                            const itemId = summaryItem.dataset.summaryItemId;
                            const componentType = summaryItem.dataset.summaryComponentType;

                            if (itemId && componentType) {
                                // Find the item in the selectedComponents array
                                const selectedArray = selectedComponents[componentType];
                                const existingIndex = selectedArray.findIndex(item => item.id === itemId);

                                if (existingIndex > -1) {
                                    // Remove the item from the array
                                    selectedArray.splice(existingIndex, 1);

                                    // Update the button state in the results section
                                    const resultsContainer = document.getElementById(`results-container_${componentType}`);
                                    if (resultsContainer) {
                                        const button = resultsContainer.querySelector(`button[data-option-id="${itemId}"][data-component-type="${componentType}"]`);
                                        if (button) {
                                            button.classList.remove('selected');
                                            button.classList.remove('btn-accent');
                                            button.classList.add('btn-select-option');
                                            button.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" /></svg> <span data-translate-key="selectThisOption">${T.selectThisOption}</span>`;
                                        }
                                    }

                                    // Update the summary panel
                                    updateSummaryPanel();
                                }
                            }
                        });
                    });
                });
            }
            function handleSelectOption(type, data, btnEl) {
                const T = translations[currentLanguage];
                const selectedArray = selectedComponents[type];
                const existingIndex = selectedArray.findIndex(item => item.id === data.id);

                // --- Capture Job Specs ---
                let jobSpecs = {};
                if (type === 'innerText') {
                    jobSpecs = {
                        trimH: safeParseFloat(document.getElementById('trim_h_innerText').value),
                        trimW: safeParseFloat(document.getElementById('trim_w_innerText').value),
                        totalPages: safeParseInt(document.getElementById('total_pages_innerText').value),
                        quantity: safeParseInt(document.getElementById('quantity_innerText').value),
                        bindingMethod: document.getElementById('binding_innerText').value
                    };
                } else if (type === 'cover') {
                    jobSpecs = {
                        trimH: safeParseFloat(document.getElementById('cover_trim_h_cover').value),
                        trimW: safeParseFloat(document.getElementById('cover_trim_w_cover').value),
                        coverType: document.getElementById('cover_type_cover').value,
                        quantity: safeParseInt(document.getElementById('quantity_cover').value)
                    };
                } else if (type === 'endpapers') {
                     jobSpecs = {
                        trimH: safeParseFloat(document.getElementById('trim_h_endpapers').value),
                        trimW: safeParseFloat(document.getElementById('trim_w_endpapers').value),
                        endpaperType: document.getElementById('endpaper_type_endpapers').value,
                        quantity: safeParseInt(document.getElementById('quantity_endpapers').value)
                    };
                }
                // --- End Capture Job Specs ---

                if (existingIndex > -1) {
                    // Item is already selected, so de-select it (remove from array)
                    selectedArray.splice(existingIndex, 1);
                    btnEl.classList.remove('selected');
                    btnEl.classList.remove('btn-accent');
                    btnEl.classList.add('btn-select-option');
                    btnEl.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" /></svg> <span data-translate-key="selectThisOption">${T.selectThisOption}</span>`;
                } else {
                    // Item is not selected, so select it (add to array)
                    // Store a copy of the data with componentType and jobSpecs to avoid issues if the data object is mutated later
                    selectedArray.push({ ...data, componentType: type, jobSpecs });
                    btnEl.classList.add('selected');
                    btnEl.classList.add('btn-accent');
                    btnEl.classList.remove('btn-select-option');
                    btnEl.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg> <span data-translate-key="optionSelected">${T.optionSelected}</span>`;
                }

                updateSummaryPanel();

                // Update spine thickness in cover tab if an inner text option with book block thickness is selected
                // Only update if the spine thickness field is empty
                if (type === 'innerText' && data.bookBlockThickness_mm && !isNaN(data.bookBlockThickness_mm) && existingIndex === -1) {
                    const spineMm = document.getElementById('spine_thickness_cover');
                    if (spineMm && !spineMm.value) {
                        spineMm.value = data.bookBlockThickness_mm.toFixed(1);
                        spineMm.dispatchEvent(new Event('input', {bubbles:true}));
                    }
                }
            }

            function displayResults(resultsData, containerEl, tabId, benchmark) {
                 if (!containerEl) return;
                const sectionEl = document.getElementById(`results-section_${tabId}`);
                sectionEl.dataset.resultsData = JSON.stringify(resultsData); sectionEl.dataset.benchmark = benchmark;
                const T = translations[currentLanguage];
                const optimal = resultsData.filter(o => o.isOptimalCandidate && !o.error && o.totalCost !== Infinity && o.wastePercent <= 1);
                const suboptimal = resultsData.filter(o => !o.isOptimalCandidate && !o.error && o.totalCost !== Infinity && o.wastePercent <= 1);
                const errors = resultsData.filter(o => o.error || o.totalCost === Infinity || o.wastePercent > 1);
                optimal.sort((a,b) => (a.wastePercent - b.wastePercent) || (a.totalCost - b.totalCost));
                suboptimal.sort((a,b) => (b.maxItemsPerSide - a.maxItemsPerSide) || (a.wastePercent - b.wastePercent) || (a.totalCost - b.totalCost));
                const bestOpt = optimal.length > 0 ? optimal[0] : (suboptimal.length > 0 ? suboptimal[0] : null);
                containerEl.innerHTML = '';
                if (optimal.length > 0 || suboptimal.length > 0 || errors.length > 0) {
                    sectionEl.classList.remove('hidden');
                    const contentEl = document.getElementById(`results-content_${tabId}`), iconEl = document.getElementById(`results-header_${tabId}`).querySelector('.toggle-icon');

                    // Expand if not user-collapsed
                    if (!contentEl.classList.contains('user-collapsed')) {
                        contentEl.classList.remove('collapsed');
                        iconEl.classList.remove('collapsed');
                    }

                    sectionEl.classList.add('pulse-glow'); setTimeout(() => { sectionEl.classList.remove('pulse-glow'); sectionEl.classList.add('fade-out-glow'); setTimeout(() => sectionEl.classList.remove('fade-out-glow'), 1500); }, 1600);
                    if (optimal.length > 0) {
                        const h = document.createElement('div'); h.className = 'col-span-full mb-2 mt-2';
                        h.innerHTML = `<h3 class="text-lg font-semibold text-primary">${T.optimalOptionsHeader} (${optimal.length})</h3><p class="text-sm text-neutral-600 dark-mode-text">${T.optimalOptionsDescription.replace('{benchmark}', benchmark || T.na)}</p>`;
                        containerEl.appendChild(h); renderResultSet(optimal, bestOpt, containerEl, tabId);
                    }
                    if (suboptimal.length > 0) {
                        const h = document.createElement('div'); h.className = 'col-span-full mb-2 mt-4';
                        h.innerHTML = `<h3 class="text-lg font-semibold text-secondary">${T.suboptimalOptionsHeader} (${suboptimal.length})</h3><p class="text-sm text-neutral-600 dark-mode-text">${T.suboptimalOptionsDescription}</p>`;
                        containerEl.appendChild(h); renderResultSet(suboptimal, bestOpt, containerEl, tabId);
                    }
                    if (errors.length > 0) {
                        const h = document.createElement('div'); h.className = 'col-span-full mb-2 mt-4';
                        h.innerHTML = `<h3 class="text-lg font-semibold text-danger">${T.errorOptionsHeader} (${errors.length})</h3><p class="text-sm text-neutral-600 dark-mode-text">${T.errorOptionsDescription}</p>`;
                        containerEl.appendChild(h); renderResultSet(errors, null, containerEl, tabId);
                    }
                    updateCollapsibleMaxHeight(contentEl);
                } else { sectionEl.classList.add('hidden'); showAlert('alertNoResults'); }
            }
            function renderResultSet(options, bestOpt, container, type) {
                const T = translations[currentLanguage];
                options.forEach(opt => {
                    const isBest = bestOpt && opt.id === bestOpt.id && !opt.error;
                    const card = document.createElement('div'); card.className = `result-card relative ${isBest ? 'best-option' : ''}`;
                    let grainTxt = T.na, grainCls = '';
                    if(!opt.error){
                        switch(opt.grainAlignmentStatus) {
                            case 'Aligned': grainTxt = T.grainAligned; grainCls = 'grain-aligned'; break;
                            case 'Misaligned': grainTxt = T.grainMisaligned; grainCls = 'grain-misaligned'; break;
                            default: grainTxt = opt.grainAlignmentStatus || T.na;
                        }
                    }
                    let pressNote = "";
                    if (opt.pressSizeNoteKey === 'resNoteRotated') pressNote = T.resNoteRotated;
                    else if (opt.pressSizeNoteKey === 'resNoteTrimmed') pressNote = T.resNoteTrimmed.replace('{h}', opt.originalH?.toFixed(1)).replace('{w}', opt.originalW?.toFixed(1));

                    let inputNote = "";
                    if (opt.inputNoteKey === 'resNoteHeightInferred') inputNote = T.resNoteHeightInferred;
                    else if (opt.inputNoteKey === 'resNoteWidthInferred') inputNote = T.resNoteWidthInferred;

                    // Roll optimization note removed as it's redundant
                    let rollOptNote = "";

                    let layoutDesc = T.na;
                    if (!opt.error && opt.layoutDescKey && T[opt.layoutDescKey]) {
                        if (opt.layoutDescKey === 'layoutDescSpreadAcross' && opt.winningLayoutAcrossPages > 0) layoutDesc = T.layoutDescSpreadAcross.replace('{num}', opt.winningLayoutAcrossPages / 2);
                        else if (opt.layoutDescKey === 'layoutDescSpreadDown' && opt.winningLayoutDownPages > 0) layoutDesc = T.layoutDescSpreadDown.replace('{num}', opt.winningLayoutDownPages / 2);
                        else layoutDesc = T[opt.layoutDescKey] || T.na;
                    }

                    const errMsg = opt.error && opt.errorMessageKey ? `${T.resErrorPrefix}: ${T[opt.errorMessageKey]||opt.errorMessageKey}`:'';
                    let errDetail = ''; if(opt.error && opt.errorMessageKey === 'errorUsableAreaNegative') errDetail = `(${opt.usableH?.toFixed(1)}x${opt.usableW?.toFixed(1)})`;
                    const fullErr = errMsg + (errDetail ? ` ${errDetail}`:'');
                    let badges = ''; if(isBest) badges += `<span class="best-option-badge">${T.mostEfficientBadge}</span>`;

                    let specMetricsHTML = '', costUnitLbl = T.resCostPerBook, costUnitVal = opt.costPerBook, itemsPerSideLbl = T.resPagesPerSide;
                    let untrimmedPageH = opt.layoutPageH, untrimmedPageW = opt.layoutPageW; // Default to item's bleed dims

                    if (type === 'innerText') {
                        specMetricsHTML = `<div><div class="result-label">${T.resUntrimmedPage}</div><div class="result-value">${(untrimmedPageH?.toFixed(1) || T.na)} H × ${(untrimmedPageW?.toFixed(1) || T.na)} W</div></div>
                                           <div><div class="result-label">${T.resLayoutFit}</div><div class="result-value">${opt.error ? T.na : (opt.winningLayoutDownPages || 0) + ' × ' + (opt.winningLayoutAcrossPages || 0)}</div><div class="result-value-small">${layoutDesc}</div></div>
                                           <div><div class="result-label">${itemsPerSideLbl}</div><div class="result-value">${opt.error || opt.maxItemsPerSide <= 0 ? T.na : opt.maxItemsPerSide}</div></div>
                                           <div><div class="result-label">${T.resResultingSig}</div><div class="result-value">${opt.error || opt.pagesPerSheetOutput <= 0 ? T.na : opt.pagesPerSheetOutput + 'p'}</div></div>`;
                    } else if (type === 'cover') {
                        costUnitLbl = T.resCostPerCover; itemsPerSideLbl = T.resItemsPerSide;
                         specMetricsHTML = `<div><div class="result-label">${T.resItemsPerSide}</div><div class="result-value">${opt.error || opt.maxItemsPerSide <= 0 ? T.na : opt.maxItemsPerSide}</div></div>`;
                    } else if (type === 'endpapers') {
                        costUnitLbl = T.resCostPerEndpaperSet; itemsPerSideLbl = T.resLeavesPerSide;
                        specMetricsHTML = `<div><div class="result-label">${itemsPerSideLbl}</div><div class="result-value">${opt.error || opt.maxItemsPerSide <= 0 ? T.na : opt.maxItemsPerSide}</div></div>
                                           <div><div class="result-label">${T.resResultingSig}</div><div class="result-value">${opt.error || opt.pagesPerSheetOutput <= 0 ? T.na : opt.pagesPerSheetOutput + 'pp'}</div></div>`;
                    }
                    card.innerHTML = `${badges}<h3 class="font-semibold text-lg mb-2 mt-1">${opt.paperName} ${opt.error?'('+T.resErrorPrefix+')':''}</h3>
                        ${opt.error?`<p class="text-sm text-red-500 dark:text-red-400 font-medium mb-2">${fullErr}</p>`:''}
                        ${(inputNote||pressNote)?`<p class="text-xs text-neutral-500 dark:text-neutral-400 italic mb-3">${inputNote.replace(/[()]/g, '')}${inputNote&&pressNote?' — ':''}${pressNote.replace(/[()]/g, '')}</p>`:''}
                        <div class="grid grid-cols-2 gap-x-4 gap-y-3 mb-3">
                            <div><div class="result-label">${T.resInputSheet}</div><div class="result-value">${opt.sheetH_input?.toFixed(1)||T.na} H × ${opt.sheetW_input?.toFixed(1)||T.na} W</div><div class="result-value-small">(${mmToIn(opt.sheetH_input).toFixed(2)}" × ${mmToIn(opt.sheetW_input).toFixed(2)}")</div></div>
                            <div><div class="result-label">${T.resPressSize}</div><div class="result-value">${opt.pressH?.toFixed(1)||T.na} H × ${opt.pressW?.toFixed(1)||T.na} W</div><div class="result-value-small">(${mmToIn(opt.pressH).toFixed(2)}" × ${mmToIn(opt.pressW).toFixed(2)}")</div></div>
                            <div><div class="result-label">${T.resUsableArea}</div><div class="result-value">${opt.usableH?.toFixed(1)||T.na} H × ${opt.usableW?.toFixed(1)||T.na} W</div><div class="result-value-small">(${mmToIn(opt.usableH).toFixed(2)}" × ${mmToIn(opt.usableW).toFixed(2)}")</div></div>
                            <div><div class="result-label">${T.resGrainAlignment}</div><div class="result-value ${grainCls}">${grainTxt}</div></div>
                            <div><div class="result-label">${T.resUntrimmedPage}</div><div class="result-value">${opt.layoutPageH?.toFixed(1)||T.na} H × ${opt.layoutPageW?.toFixed(1)||T.na} W</div><div class="result-value-small">(${mmToIn(opt.layoutPageH).toFixed(2)}" × ${mmToIn(opt.layoutPageW).toFixed(2)}")</div></div>
                            <div><div class="result-label">${T.resImposedArea}</div><div class="result-value">${opt.occupiedHeight?.toFixed(1)||T.na} H × ${opt.occupiedWidth?.toFixed(1)||T.na} W</div><div class="result-value-small">(${mmToIn(opt.occupiedHeight).toFixed(2)}" × ${mmToIn(opt.occupiedWidth).toFixed(2)}")</div></div>
                            <div><div class="result-label">${T.resLayoutFit}</div><div class="result-value">${opt.error ? T.na : (opt.winningLayoutDownPages || 0) + ' × ' + (opt.winningLayoutAcrossPages || 0)}</div><div class="result-value-small">${layoutDesc}</div></div>
                            <div><div class="result-label">${T.resPagesPerSide}</div><div class="result-value">${opt.error || opt.maxItemsPerSide <= 0 ? T.na : opt.maxItemsPerSide}</div></div>
                            <div><div class="result-label">${T.resSheetUtilization}</div><div class="result-value">${opt.error||opt.maxItemsPerSide===0?T.na:((1-opt.wastePercent)*100).toFixed(1)+'%'}</div></div>
                            <div><div class="result-label">${T.resResultingSig}</div><div class="result-value">${opt.error || opt.pagesPerSheetOutput <= 0 ? T.na : opt.pagesPerSheetOutput + 'p'}</div></div>
                            <div><div class="result-label">${T.resTotalSheets}</div><div class="result-value">${opt.error?T.na:opt.totalSheetsNeeded?.toLocaleString()||T.na}</div></div>
                            <div><div class="result-label">${T.resCostPerSheet}</div><div class="result-value">${opt.error?T.na:'$'+(opt.costPerSheet?.toFixed(2)||T.na)}</div></div>
                            <div class="col-span-2"><div class="result-label">${T.resTotalCost}</div><div class="result-value text-xl font-bold">${opt.error||opt.totalCost===Infinity?T.na:'$'+opt.totalCost?.toFixed(2)}</div></div>
                        </div>
                        <div class="grid grid-cols-2 gap-x-4 gap-y-2 mt-2 border-t border-neutral-200 dark:border-neutral-700 pt-2">
                            ${type==='innerText'?`<div><div class="result-label">${T.resBookBlockThickness}</div><div class="result-value">${opt.error||isNaN(opt.bookBlockThickness_mm)?T.na:opt.bookBlockThickness_mm.toFixed(2)+' mm'}</div></div>`:''}
                            <div><div class="result-label">${costUnitLbl}</div><div class="result-value">${opt.error||isNaN(costUnitVal)?T.na:'$'+costUnitVal.toFixed(2)}</div></div>
                        </div>
                        <div class="text-xs text-neutral-500 mt-2 pt-2 border-t border-neutral-200 dark:border-neutral-700 dark-mode-text">
                            ${T.resSource}: ${opt.source==='Pre-Cut'?T.sourcePreCut:T.sourceRoll} | ${T.resInputGrain}: ${opt.grainDirInput==='Height'?T.grainHeight:T.grainWidth} | ${T.resGsm}: ${opt.gsm||T.na}g/m² | ${T.colCaliper.split(' ')[0]}: ${opt.caliperMicrons>0?opt.caliperMicrons.toFixed(0)+' µm':T.na}
                        </div>
                        ${!opt.error?`<button class="btn ${selectedComponents[type].some(item => item.id === opt.id) ? 'btn-accent selected' : 'btn-select-option'} mt-4 w-full justify-center" data-option-id="${opt.id}" data-component-type="${type}">${selectedComponents[type].some(item => item.id === opt.id)?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span data-translate-key="optionSelected">${T.optionSelected}</span>`:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" /></svg><span data-translate-key="selectThisOption">${T.selectThisOption}</span>`}</button>`:''}
                    `;
                    container.appendChild(card);
                    if(!opt.error){
                        const selBtn = card.querySelector('.btn-select-option, .btn-accent.selected');
                        if (selBtn) {
                            selBtn.addEventListener('click',(e)=>handleSelectOption(type,opt,e.currentTarget));
                        }
                    }
                });
            }

            // --- Main Calculation Orchestrator ---
            function calculateAndStoreResults(initiatingTabId) {
                showLoadingOverlay();
                setTimeout(() => {
                    try {
                        let jobInputs, bleedDims, benchmarkValue;
                        const results = [];
                        const maxPressH = 720, maxPressW = 1020;
                        const paperOptionsTableBody = document.getElementById(`paper-options-table_${initiatingTabId}`).querySelector('tbody');
                        const rows = paperOptionsTableBody.querySelectorAll('tr');
                        let anyRowHasError = false;

                        // --- Get and Validate Component-Specific Inputs ---
                        if (initiatingTabId === 'innerText') {
                            jobInputs = getJobInputsAndValidate_InnerText();
                            if (!jobInputs) { hideLoadingOverlay(); return; }
                            bleedDims = calculateBleedDimensions_InnerText(jobInputs.trimW, jobInputs.trimH, jobInputs.bleed, jobInputs.bindingMethod);
                            if (!bleedDims) { showAlert('errorPageDimsInvalid'); hideLoadingOverlay(); return; }
                            benchmarkValue = determineOptimalLayoutBenchmark_InnerText(maxPressH, maxPressW, jobInputs.gripper, jobInputs.colorBar, jobInputs.lip, jobInputs.bleed, jobInputs.trimW, jobInputs.trimH, jobInputs.bindingMethod);
                        } else if (initiatingTabId === 'cover') {
                            jobInputs = getJobInputsAndValidate_Cover();
                            if (!jobInputs) { hideLoadingOverlay(); return; }
                            // For cover, bleedDims are the dimensions of one flat cover spread
                            bleedDims = calculateBleedDimensions_Cover(
                                jobInputs.trimH,
                                jobInputs.trimW,
                                jobInputs.spineThickness,
                                jobInputs.bleed,
                                jobInputs.coverType,      // Pass coverType
                                jobInputs.turnInAllowance, // Pass turnInAllowance
                                jobInputs.flapWidth       // Pass flapWidth
                            );
                            if (!bleedDims) { showAlert('errorCoverSpreadSize'); hideLoadingOverlay(); return; }
                            benchmarkValue = determineOptimalLayoutBenchmark_Cover(maxPressH, maxPressW, jobInputs.gripper, jobInputs.colorBar, bleedDims.layoutPageH, bleedDims.layoutPageW);
                        } else if (initiatingTabId === 'endpapers') {
                            jobInputs = getJobInputsAndValidate_Endpapers();
                            if (!jobInputs) { hideLoadingOverlay(); return; }
                            // For endpapers, bleedDims are the dimensions of one flat endpaper spread
                            bleedDims = calculateBleedDimensions_Endpapers(jobInputs.trimH, jobInputs.trimW, jobInputs.bleed);
                            if (!bleedDims) { showAlert('errorEndpaperSpreadSize'); hideLoadingOverlay(); return; }
                             benchmarkValue = determineOptimalLayoutBenchmark_Endpapers(maxPressH, maxPressW, jobInputs.gripper, jobInputs.colorBar, bleedDims.layoutPageH, bleedDims.layoutPageW);
                    } else {
                            console.error("Unknown tab for calculation:", initiatingTabId);
                            hideLoadingOverlay(); return;
                        }

                        // --- Evaluate Each Paper Stock for the Current Tab ---
                        rows.forEach(row => {
                            const paperData = getPaperOptionDataAndValidate(row, initiatingTabId);
                            if (paperData.error) { results.push(paperData); anyRowHasError = true; return; }

                            const initialFit = determineInitialPressFit(paperData, maxPressH, maxPressW);
                            if (initialFit.error) { results.push({ ...paperData, ...initialFit }); anyRowHasError = true; return; }

                            const pressFit = applyPressLimitsAndGetGrain(initialFit.sheetH_for_initial_fit, initialFit.sheetW_for_initial_fit, maxPressH, maxPressW, paperData.grainDirInput);
                            const usableArea = calculateUsableArea(pressFit.pressH, pressFit.pressW, jobInputs.gripper, jobInputs.colorBar, initiatingTabId === 'innerText' ? jobInputs.lip : 0);
                            if (usableArea.error) { results.push({ ...paperData, ...initialFit, ...pressFit, ...usableArea, originalH: paperData.sheetH_input, originalW: paperData.sheetW_input }); anyRowHasError = true; return; }

                            let baseResult = {
                                ...paperData, ...initialFit, ...pressFit, ...usableArea,
                                trimW: jobInputs.trimW, trimH: jobInputs.trimH, bleed: jobInputs.bleed,
                                bindingMethod: jobInputs.bindingMethod, // For context, might differ from cover/endpaper "binding"
                                layoutPageH: bleedDims.layoutPageH, // Item height with bleed
                                layoutPageW: bleedDims.layoutPageW, // Item width with bleed
                                originalH: paperData.sheetH_input, originalW: paperData.sheetW_input,
                                costPerBook: NaN, maxItemsPerSide: 0, pagesPerSheetOutput: 0, totalSheetsNeeded: 0,
                                costPerSheet: NaN, totalCost: Infinity, wastePercent: 1.1, utilisationRate: -0.1,
                                isOptimalCandidate: false, layoutOrientation: '', layoutDescKey: '',
                                winningLayoutDownPages: 0, winningLayoutAcrossPages: 0, occupiedHeight: 0, occupiedWidth: 0, grainAlignmentStatus: ''
                                // For cover/endpapers, jobInputs might need more specific fields like jobInputs.coverType
                            };
                            if (initiatingTabId === 'cover') baseResult.coverType = jobInputs.coverType;
                            if (initiatingTabId === 'endpapers') baseResult.endpaperType = jobInputs.endpaperType;


                            // Item dimensions for layout: bleedDims.layoutPageH and bleedDims.layoutPageW
                            // The 'applyEvenRule' booleans are now correctly set based on 'innerText' tab.
                            let candidateA_Data = calculateSingleLayoutFit_ComponentSpecific(initiatingTabId, usableArea.usableH, usableArea.usableW, bleedDims.layoutPageH, bleedDims.layoutPageW, false, (initiatingTabId === 'innerText'), jobInputs);
                            let candidateB_Data = calculateSingleLayoutFit_ComponentSpecific(initiatingTabId, usableArea.usableH, usableArea.usableW, bleedDims.layoutPageW, bleedDims.layoutPageH, (initiatingTabId === 'innerText'), false, jobInputs);


                            let candidateA = null, candidateB = null;
                            if(candidateA_Data){
                                candidateA = {...baseResult, ...candidateA_Data};
                                candidateA.layoutOrientation = 'A'; candidateA.maxItemsPerSide = candidateA_Data.fit;
                                candidateA.winningLayoutDownPages = candidateA_Data.layoutDown; candidateA.winningLayoutAcrossPages = candidateA_Data.layoutAcross;
                                // For Inner Text, layoutDescKey helps describe the spread orientation
                                candidateA.layoutDescKey = (initiatingTabId === 'innerText' && candidateA_Data.fit > 0) ? 'layoutDescSpreadAcross' : '';
                                candidateA.grainAlignmentStatus = baseResult.isLongGrainParallelToPressH ? 'Aligned' : 'Misaligned';
                            }
                            if(candidateB_Data){
                                candidateB = {...baseResult, ...candidateB_Data};
                                candidateB.layoutOrientation = 'B'; candidateB.maxItemsPerSide = candidateB_Data.fit;
                                candidateB.winningLayoutDownPages = candidateB_Data.layoutDown; candidateB.winningLayoutAcrossPages = candidateB_Data.layoutAcross;
                                candidateB.layoutDescKey = (initiatingTabId === 'innerText' && candidateB_Data.fit > 0) ? 'layoutDescSpreadDown' : '';
                                candidateB.grainAlignmentStatus = !baseResult.isLongGrainParallelToPressH ? 'Aligned' : 'Misaligned';
                             }

                            let bestCandidate = selectWinningCandidate(candidateA, candidateB, jobInputs.alignmentMode);

                            if (bestCandidate) {
                                bestCandidate = { ...bestCandidate };
                                finalizeRollDimensions(bestCandidate, jobInputs.gripper, jobInputs.colorBar, initiatingTabId === 'innerText' ? jobInputs.lip : 0);
                                calculateFinalMetrics_ComponentSpecific(initiatingTabId, bestCandidate, jobInputs, benchmarkValue);
                                results.push(bestCandidate);
                                if (bestCandidate.error) anyRowHasError = true;
                            } else {
                                if (!candidateA && !candidateB) {
                                    baseResult.error = true; baseResult.errorMessageKey = "errorCannotFit"; results.push(baseResult);
                                    anyRowHasError = true;
                                }
                            }
                        });

                        const resultsContainerEl = document.getElementById(`results-container_${initiatingTabId}`);
                        if (results.length === 0) {
                            const anyRowsEntered = paperOptionsTableBody.querySelectorAll('tr').length > 0;
                            if (anyRowsEntered && !anyRowHasError) showAlert('alertNoResultsForAlignment', { mode: jobInputs.alignmentMode });
                            else if (!anyRowsEntered) showAlert('alertNoResults');
                            document.getElementById(`results-section_${initiatingTabId}`).classList.add('hidden');
                        } else {
                            displayResults(results, resultsContainerEl, initiatingTabId, benchmarkValue);
                        }

                    } catch (error) {
                        console.error(`Unexpected error during ${initiatingTabId} calculation:`, error);
                        showAlert("An unexpected error occurred. Please check console.");
                        document.getElementById(`results-section_${initiatingTabId}`).classList.add('hidden');
                    } finally {
                        hideLoadingOverlay();
                    }
                }, 10);
            }

            // --- Component-Specific Input & Calculation Helpers ---
            // (These need to be fully fleshed out with distinct logic for each component)

            // --- InnerText Specific ---
            function getJobInputsAndValidate_InnerText() {
                const ids = { trimW: 'trim_w_innerText', trimH: 'trim_h_innerText', totalPages: 'total_pages_innerText', quantity: 'quantity_innerText', spoilage: 'spoilage_pct_innerText', bleed: 'bleed_innerText', gripper: 'gripper_innerText', colorBar: 'color_bar_innerText', lip: 'lip_innerText'};
                const el = {}; Object.keys(ids).forEach(k => el[k] = document.getElementById(ids[k]));
                Object.values(el).forEach(i => i.classList.remove('input-error')); let hasErr = false, errKey = 'alertInvalidInputs';
                const val = {
                    trimW: safeParseFloat(el.trimW.value), trimH: safeParseFloat(el.trimH.value),
                    totalPages: safeParseInt(el.totalPages.value), quantity: safeParseInt(el.quantity.value),
                    spoilagePct: safeParseFloat(el.spoilage.value), bleed: safeParseFloat(el.bleed.value),
                    gripper: safeParseFloat(el.gripper.value), colorBar: safeParseFloat(el.colorBar.value),
                    lipVal: safeParseFloat(el.lip.value)
                };
                if(val.trimW <= 0) {el.trimW.classList.add('input-error'); hasErr=true;} if(val.trimH <= 0) {el.trimH.classList.add('input-error'); hasErr=true;}
                if(val.totalPages <= 0 || val.totalPages % 2 !== 0) {
                    el.totalPages.classList.add('input-error');
                    hasErr=true;
                    if (val.totalPages <= 0) {
                        errKey='alertTotalPagesPositive'; // More specific error message
                    } else {
                        errKey='alertTotalPagesEven'; // Even number validation
                    }
                }
                if(val.quantity <= 0) {el.quantity.classList.add('input-error'); hasErr=true;}
                ['spoilagePct','bleed','gripper','colorBar','lipVal'].forEach(k => { if(val[k]<0) {el[k==='lipVal'?'lip':k].classList.add('input-error'); hasErr=true; errKey='alertNegativeProdParams';}});
                if(hasErr){showAlert(errKey); return null;}
                const dblLip = document.getElementById('double-lip-switcher_innerText').classList.contains('active');
                const lip = dblLip ? val.lipVal*2 : val.lipVal;
                const align = document.getElementById('alignment-mode-switcher_innerText').classList.contains('misaligned') ? 'Misaligned' : 'Aligned';
                const binding = document.getElementById('binding_innerText').value;
                return { ...val, spoilagePct: val.spoilagePct/100, lip, alignmentMode: align, bindingMethod: binding, isDoubleLipActive: dblLip};
            }
            function calculateBleedDimensions_InnerText(trimW, trimH, bleed, bindingMethod) {
                 let layoutPageH = 0, layoutPageW = 0; const fourSide = ['singlePage', 'wireO'];
                if (fourSide.includes(bindingMethod)) { layoutPageH = trimH + 2*bleed; layoutPageW = trimW + 2*bleed; }
                else { layoutPageH = trimH + 2*bleed; layoutPageW = trimW + bleed; } // Spread logic
                return (layoutPageH > 0 && layoutPageW > 0) ? { layoutPageH, layoutPageW } : null;
            }
            function determineOptimalLayoutBenchmark_InnerText(maxPressH,maxPressW,gripper,colorBar,lip,bleed,trimW,trimH,bindingMethod){
                const lipM = lip; const usableH = maxPressH-gripper-colorBar; const usableW = maxPressW-lipM;
                if(usableH<=0||usableW<=0) return 0;
                const dims=calculateBleedDimensions_InnerText(trimW,trimH,bleed,bindingMethod);
                if(!dims || !dims.layoutPageH || !dims.layoutPageW) return 0;
                const {layoutPageH,layoutPageW} = dims;

                let fitA=0;
                const dA=floor(usableH/layoutPageH); const aA_i=floor(usableW/layoutPageW); const aA=(aA_i>0)?floor(aA_i/2)*2:0; fitA=dA*aA;
                fitA=isNaN(fitA)?0:fitA;

                let fitB=0;
                const dB_i=floor(usableH/layoutPageW); const dB=(dB_i>0)?floor(dB_i/2)*2:0; const aB=floor(usableW/layoutPageH); fitB=dB*aB;
                fitB=isNaN(fitB)?0:fitB;

                return Math.min(Math.max(fitA,fitB),16); // Max 16 pages per side folding limit
            }

            // --- Cover Specific ---
            function getJobInputsAndValidate_Cover() {
                const innerH = safeParseFloat(document.getElementById('trim_h_innerText').value), innerW = safeParseFloat(document.getElementById('trim_w_innerText').value);
                const ids = {trimH:'cover_trim_h_cover', trimW:'cover_trim_w_cover', trimHIn:'cover_trim_h_in_cover', trimWIn:'cover_trim_w_in_cover', spine:'spine_thickness_cover', quantity:'quantity_cover', spoilage:'spoilage_pct_cover', bleed:'bleed_cover', gripper:'gripper_cover', colorBar:'color_bar_cover', turnIn:'turn_in_allowance_cover', flap:'flap_width_cover'};
                const el = {}; Object.keys(ids).forEach(k => el[k] = document.getElementById(ids[k]));

                // If height/width values are not set but inner text values exist, carry them over and trigger conversion
                if(!el.trimH.value && innerH > 0) {
                    el.trimH.value = innerH.toFixed(1);
                    // Also update the inches field by triggering the input event
                    el.trimH.dispatchEvent(new Event('input', {bubbles:true}));
                }

                if(!el.trimW.value && innerW > 0) {
                    el.trimW.value = innerW.toFixed(1);
                    // Also update the inches field by triggering the input event
                    el.trimW.dispatchEvent(new Event('input', {bubbles:true}));
                }

                Object.values(el).forEach(i => { if(i) i.classList.remove('input-error'); }); // Check if element exists before accessing classList
                let hasErr = false, errKey = 'alertInvalidInputs';

                const coverType = document.getElementById('cover_type_cover').value; // Get cover type

                const val = {
                    trimH: safeParseFloat(el.trimH.value), trimW: safeParseFloat(el.trimW.value), spineThickness: safeParseFloat(el.spine.value),
                    quantity: safeParseInt(el.quantity.value), spoilagePct: safeParseFloat(el.spoilage.value),
                    bleed: safeParseFloat(el.bleed.value), gripper: safeParseFloat(el.gripper.value), colorBar: safeParseFloat(el.colorBar.value),
                    turnInAllowance: (coverType === 'hardcover' && el.turnIn) ? safeParseFloat(el.turnIn.value, 0) : 0, // Only consider if hardcover
                    flapWidth: (coverType === 'dustJacket' && el.flap) ? safeParseFloat(el.flap.value, 0) : 0 // Only consider if dust jacket
                };
                if(val.trimH<=0){el.trimH.classList.add('input-error');hasErr=true;} if(val.trimW<=0){el.trimW.classList.add('input-error');hasErr=true;}
                // Spine can be 0 (e.g. saddle stitch, though cover usually isn't saddle stitched with text)
                // For perfect bound / case bound, spine must be >0 if calculated from text, or user input.
                // For this generic validation, only negative is an error. More complex validation could be added.
                if(val.spineThickness<0){el.spine.classList.add('input-error');hasErr=true; errKey='alertNegativeProdParams';}

                // Validate turnInAllowance only if it's relevant (hardcover) and the element exists
                if (coverType === 'hardcover' && el.turnIn && val.turnInAllowance < 0) {
                    el.turnIn.classList.add('input-error');
                    hasErr = true;
                    errKey = 'alertNegativeProdParams';
                }

                // Validate flapWidth only if it's relevant (dust jacket) and the element exists
                if (coverType === 'dustJacket' && el.flap && val.flapWidth < 0) {
                    el.flap.classList.add('input-error');
                    hasErr = true;
                    errKey = 'alertNegativeProdParams';
                }

                // Validate other parameters
                ['spoilagePct','bleed','gripper','colorBar'].forEach(k => { if(val[k]<0) {el[k].classList.add('input-error'); hasErr=true; errKey='alertNegativeProdParams';}});

                if(hasErr){showAlert(errKey);return null;}
                const align = document.getElementById('alignment-mode-switcher_cover').classList.contains('misaligned') ? 'Misaligned' : 'Aligned';
                return {...val, spoilagePct: val.spoilagePct/100, alignmentMode: align, coverType};
            }
            function calculateBleedDimensions_Cover(trimH, trimW, spine, bleed, coverType, turnInAllowance, flapWidth) {
                // This function calculates the dimensions of ONE FLAT COVER SPREAD
                // Calculate core dimensions based on cover type
                let coreLayoutPageH = trimH + (2 * bleed);
                let coreLayoutPageW;

                if (coverType === 'dustJacket' && flapWidth > 0) {
                    // Dust Jacket formula: Total Width = (Book Width × 2) + (Flap Width × 2) + Spine Width + (Bleed × 2)
                    coreLayoutPageW = (2 * trimW) + (2 * flapWidth) + spine + (2 * bleed);
                } else {
                    // Standard cover formula: Back_Cover_W + Spine_W + Front_Cover_W + Bleeds on outer edges
                    coreLayoutPageW = (2 * trimW) + spine + (2 * bleed);
                }

                let finalLayoutPageH = coreLayoutPageH;
                let finalLayoutPageW = coreLayoutPageW;

                // Add turn-in allowance if it's a hardcover and allowance is positive
                if (coverType === 'hardcover' && turnInAllowance > 0) {
                    finalLayoutPageH += (2 * turnInAllowance);
                    finalLayoutPageW += (2 * turnInAllowance);
                }

                return (finalLayoutPageH > 0 && finalLayoutPageW > 0) ? { layoutPageH: finalLayoutPageH, layoutPageW: finalLayoutPageW } : null;
            }
            function determineOptimalLayoutBenchmark_Cover(maxPressH,maxPressW,gripper,colorBar,itemH,itemW) {
                // itemH and itemW are the dimensions of one flat cover spread (from calculateBleedDimensions_Cover)
                const usableH = maxPressH-gripper-colorBar; const usableW = maxPressW;
                if(usableH<=0||usableW<=0||itemH<=0||itemW<=0) return 0;
                // How many flat cover spreads can fit (N-up)
                const fitA = floor(usableH/itemH)*floor(usableW/itemW); // Item H || Press H
                const fitB = floor(usableH/itemW)*floor(usableW/itemH); // Item W || Press H (rotated)
                return Math.max(fitA,fitB); // No 16-item limit for covers usually, it's about N-up of the flat spread.
            }

            // --- Endpapers Specific ---
            function getJobInputsAndValidate_Endpapers() {
                const innerH = safeParseFloat(document.getElementById('trim_h_innerText').value), innerW = safeParseFloat(document.getElementById('trim_w_innerText').value);
                const ids = {trimH:'trim_h_endpapers', trimW:'trim_w_endpapers', trimHIn:'trim_h_in_endpapers', trimWIn:'trim_w_in_endpapers', quantity:'quantity_endpapers', spoilage:'spoilage_pct_endpapers', bleed:'bleed_endpapers', gripper:'gripper_endpapers', colorBar:'color_bar_endpapers'};
                const el = {}; Object.keys(ids).forEach(k => el[k] = document.getElementById(ids[k]));

                // If height/width values are not set but inner text values exist, carry them over and trigger conversion
                if(!el.trimH.value && innerH > 0) {
                    el.trimH.value = innerH.toFixed(1);
                    // Also update the inches field by triggering the input event
                    el.trimH.dispatchEvent(new Event('input', {bubbles:true}));
                }

                if(!el.trimW.value && innerW > 0) {
                    el.trimW.value = innerW.toFixed(1);
                    // Also update the inches field by triggering the input event
                    el.trimW.dispatchEvent(new Event('input', {bubbles:true}));
                }
                Object.values(el).forEach(i => i.classList.remove('input-error')); let hasErr = false, errKey = 'alertInvalidInputs';
                const val = {
                    trimH: safeParseFloat(el.trimH.value), trimW: safeParseFloat(el.trimW.value), quantity: safeParseInt(el.quantity.value),
                    spoilagePct: safeParseFloat(el.spoilage.value), bleed: safeParseFloat(el.bleed.value), gripper: safeParseFloat(el.gripper.value),
                    colorBar: safeParseFloat(el.colorBar.value)
                };
                if(val.trimH<=0){el.trimH.classList.add('input-error');hasErr=true;} if(val.trimW<=0){el.trimW.classList.add('input-error');hasErr=true;}
                ['spoilagePct','bleed','gripper','colorBar'].forEach(k => { if(val[k]<0) {el[k].classList.add('input-error'); hasErr=true; errKey='alertNegativeProdParams';}});
                if(hasErr){showAlert(errKey);return null;}
                const align = document.getElementById('alignment-mode-switcher_endpapers').classList.contains('misaligned') ? 'Misaligned' : 'Aligned';
                const endpaperType = document.getElementById('endpaper_type_endpapers').value;
                return {...val, spoilagePct: val.spoilagePct/100, alignmentMode: align, endpaperType};
            }
            function calculateBleedDimensions_Endpapers(trimH,trimW,bleed) {
                // An endpaper spread is typically TrimH x (2 * TrimW) before bleed.
                // layoutPageH is the height of the spread.
                const layoutPageH = trimH + (2 * bleed); // Top and bottom bleed
                // layoutPageW is the width of the spread.
                // For a spread (2 * TrimW), bleed is typically only on the outer edge, not the spine fold.
                const layoutPageW = (2 * trimW) + bleed; // Bleed on one side of the spread width
                // If bleed is needed on both fore-edges of the double-page spread (less common for single sheet endpapers that fold):
                // const layoutPageW = (2 * trimW) + (2 * bleed);
                return (layoutPageH > 0 && layoutPageW > 0) ? {layoutPageH, layoutPageW} : null;
            }
            function determineOptimalLayoutBenchmark_Endpapers(maxPressH,maxPressW,gripper,colorBar,itemH,itemW) {
                // itemH, itemW are dimensions of one flat endpaper spread.
                return determineOptimalLayoutBenchmark_Cover(maxPressH,maxPressW,gripper,colorBar,itemH,itemW); // Same logic as cover for N-up
            }

            // --- Generic Calculation Helpers (Used by all tabs, or adapted within component-specific flows) ---
            function getPaperOptionDataAndValidate(row, tabId) {
                const sheetHInput = row.querySelector('.sheet-h'); const sheetWInput = row.querySelector('.sheet-w');
                const caliperMicronsInput = row.querySelector('.caliper-microns'); const costReamInput = row.querySelector('.cost-ream');
                const gsmInput = row.querySelector('.gsm'); const costTonneInput = row.querySelector('.cost-tonne');
                [sheetHInput, sheetWInput, caliperMicronsInput, costReamInput, gsmInput, costTonneInput].forEach(input => input?.classList.remove('input-error'));

                const paperData = {
                    id: row.dataset.rowId,
                    paperName: row.querySelector('.paper-name').value || `${translations[currentLanguage].newOptionDefaultName.replace('{num}',row.dataset.rowId)} (${tabId})`,
                    source: row.querySelector('.source').value, sheetH_input: safeParseFloat(sheetHInput.value),
                    sheetW_input: safeParseFloat(sheetWInput.value), grainDirInput: row.querySelector('.grain-dir').value,
                    caliperMicrons: safeParseFloat(caliperMicronsInput.value), costReam: safeParseFloat(costReamInput.value),
                    gsm: safeParseFloat(gsmInput.value), costTonne: safeParseFloat(costTonneInput.value),
                    error: false, errorMessageKey: "alertInvalidInputs"
                };
                let rowHasError = false;
                // Negative value checks
                if (paperData.sheetH_input < 0) { sheetHInput.classList.add('input-error'); rowHasError = true; paperData.errorMessageKey = 'alertNegativeProdParams'; }
                if (paperData.sheetW_input < 0) { sheetWInput.classList.add('input-error'); rowHasError = true; paperData.errorMessageKey = 'alertNegativeProdParams'; }
                if (paperData.caliperMicrons < 0) { caliperMicronsInput.classList.add('input-error'); rowHasError = true; paperData.errorMessageKey = 'alertNegativeProdParams'; }
                if (paperData.costReam < 0 && paperData.source === 'Pre-Cut') { costReamInput.classList.add('input-error'); rowHasError = true; paperData.errorMessageKey = 'alertNegativeProdParams'; }
                if (paperData.gsm < 0) { gsmInput.classList.add('input-error'); rowHasError = true; paperData.errorMessageKey = 'alertNegativeProdParams'; }
                if (paperData.costTonne < 0 && paperData.source === 'Roll') { costTonneInput.classList.add('input-error'); rowHasError = true; paperData.errorMessageKey = 'alertNegativeProdParams'; }

                // Zero or missing value checks for required fields
                if (paperData.source === 'Pre-Cut' && paperData.costReam <= 0 && !rowHasError) { costReamInput.classList.add('input-error'); rowHasError = true; paperData.errorMessageKey = 'errorMissingCostReam'; }
                if (paperData.source === 'Roll' && (paperData.gsm <= 0 || paperData.costTonne <= 0) && !rowHasError) {
                    if (paperData.gsm <= 0) gsmInput.classList.add('input-error');
                    if (paperData.costTonne <= 0) costTonneInput.classList.add('input-error');
                    rowHasError = true; paperData.errorMessageKey = 'errorMissingRollData';
                }
                // Sheet dimensions must be positive for Pre-Cut, or if explicitly entered for Roll
                if (paperData.source === 'Pre-Cut' && (paperData.sheetH_input <= 0 || paperData.sheetW_input <= 0) && !rowHasError) {
                    if (paperData.sheetH_input <= 0) sheetHInput.classList.add('input-error');
                    if (paperData.sheetW_input <= 0) sheetWInput.classList.add('input-error');
                    rowHasError = true; paperData.errorMessageKey = 'errorInvalidSheetDims';
                }
                // For Roll, if one dim is provided, it must be positive. If both are zero/negative, it's an error unless handled by inference (which determineInitialPressFit does).
                // This validation ensures that if a user *does* enter a roll dimension, it's not negative.
                if (paperData.source === 'Roll') {
                    if ((paperData.sheetH_input > 0 && paperData.sheetW_input < 0) || (paperData.sheetW_input > 0 && paperData.sheetH_input < 0)) {
                         if (paperData.sheetH_input < 0) sheetHInput.classList.add('input-error');
                         if (paperData.sheetW_input < 0) sheetWInput.classList.add('input-error');
                         rowHasError = true; paperData.errorMessageKey = 'alertNegativeProdParams';
                    }
                }

                paperData.error = rowHasError;
                return paperData;
            }
            // Function to determine initial sheet dimensions for processing.
            // For rolls, it creates a maximized hypothetical canvas.
            // It also tracks which dimension was inferred for later trimming.
            function determineInitialPressFit(paperData, maxPressH, maxPressW) {
                let sheetH_for_initial_fit;
                let sheetW_for_initial_fit;
                let inputNoteKey = "";
                // This flag indicates if the roll dimension was inferred/maximized for optimization,
                // making it eligible for trimming in finalizeRollDimensions.
                let rollDimensionOptimizedForFitting = false;
                let error = false;
                let errorMessageKey = "";

                const originalH = paperData.sheetH_input; // Parsed float or 0
                const originalW = paperData.sheetW_input; // Parsed float or 0

                if (paperData.source === 'Roll') {
                    if (originalH > 0 && originalW > 0) {
                        // SCENARIO 1: User specified BOTH H and W for the Roll.
                        // Treat dimensions as fixed for imposition, like a pre-cut sheet.
                        sheetH_for_initial_fit = originalH;
                        sheetW_for_initial_fit = originalW;
                        inputNoteKey = ""; // Or a specific key like "userSpecifiedRollDims" if needed for other logic
                        rollDimensionOptimizedForFitting = false; // Dimensions are fixed, not optimized by maximizing canvas.
                    } else if (originalW > 0 && originalH <= 0) {
                        // SCENARIO 2: User provided Roll Width (originalW), cut-off Height (originalH) is flexible.
                        sheetW_for_initial_fit = originalW; // Fixed roll width
                        sheetH_for_initial_fit = Math.max(maxPressH, maxPressW); // Maximize cut-off length for initial fit test
                        inputNoteKey = "resNoteHeightInferred";
                        rollDimensionOptimizedForFitting = true;
                    } else if (originalH > 0 && originalW <= 0) {
                        // SCENARIO 3: User provided Roll Height (originalH - assumed to be manufactured roll width here),
                        // cut-off Width (originalW) is flexible.
                        sheetH_for_initial_fit = originalH; // Fixed roll width
                        sheetW_for_initial_fit = Math.max(maxPressH, maxPressW); // Maximize cut-off length for initial fit test
                        inputNoteKey = "resNoteWidthInferred";
                        rollDimensionOptimizedForFitting = true;
                    } else {
                        // SCENARIO 4: Neither dimension (or invalid) provided for Roll.
                        error = true;
                        errorMessageKey = "errorMissingRollData"; // e.g., "Roll width must be provided."
                        sheetH_for_initial_fit = 0;
                        sheetW_for_initial_fit = 0;
                    }
                } else if (paperData.source === 'Pre-Cut') {
                    sheetH_for_initial_fit = originalH;
                    sheetW_for_initial_fit = originalW;
                    if (sheetH_for_initial_fit <= 0 || sheetW_for_initial_fit <= 0) {
                        error = true;
                        errorMessageKey = "errorInvalidSheetDims";
                    }
                    rollDimensionOptimizedForFitting = false;
                } else {
                    error = true;
                    errorMessageKey = "errorUnknownSource";
                    sheetH_for_initial_fit = 0;
                    sheetW_for_initial_fit = 0;
                }

                if (!error && (sheetH_for_initial_fit <= 0 || sheetW_for_initial_fit <= 0)) {
                    error = true;
                    errorMessageKey = "errorInvalidSheetDims"; // Should be caught earlier, but good failsafe
                }

                return {
                    sheetH_for_initial_fit: error ? 0 : parseFloat(sheetH_for_initial_fit.toFixed(1)),
                    sheetW_for_initial_fit: error ? 0 : parseFloat(sheetW_for_initial_fit.toFixed(1)),
                    inputNoteKey: inputNoteKey, // Still useful for messages like "(H inferred)"
                    // This new flag is key for controlling finalizeRollDimensions:
                    rollDimensionOptimizedForFitting: rollDimensionOptimizedForFitting,
                    error: error,
                    errorMessageKey: errorMessageKey
                };
            }
            function applyPressLimitsAndGetGrain(initialH, initialW, maxPressH, maxPressW, grainDirInput) {
                let pressH = initialH, pressW = initialW, rotated = false, noteKey = "";
                if (pressH > maxPressH || pressW > maxPressW) {
                    if (initialW <= maxPressH && initialH <= maxPressW) { pressH = initialW; pressW = initialH; rotated = true; noteKey = "resNoteRotated"; }
                    else { pressH = Math.min(initialH, maxPressH); pressW = Math.min(initialW, maxPressW); noteKey = "resNoteTrimmed"; }
                }
                const isInputGrainParallelToInputH = (grainDirInput === 'Height');
                const isLongGrainParallelToPressH = rotated ? !isInputGrainParallelToInputH : isInputGrainParallelToInputH;
                return { pressH, pressW, paperWasRotated: rotated, pressSizeNoteKey: noteKey, isLongGrainParallelToPressH };
            }
            function calculateUsableArea(pressH, pressW, gripper, colorBar, lip = 0) {
                const usableH = Math.max(0, pressH - gripper - colorBar);
                const usableW = Math.max(0, pressW - lip);
                let error = false, errorKey = "";
                if (usableH <= 0 || usableW <= 0) { error = true; errorKey = "errorUsableAreaNegative"; }
                return { usableH, usableW, error, errorMessageKey: errorKey };
            }

            function calculateSingleLayoutFit_ComponentSpecific(tabId, usableH, usableW, itemDimH, itemDimW, applyEvenRuleToHeight, applyEvenRuleToWidth, jobInputs) {
                if (itemDimH <= 0 || itemDimW <= 0) return null;

                let layoutDown_initial = floor(usableH / itemDimH);
                let layoutAcross_initial = floor(usableW / itemDimW);
                let layoutDown = layoutDown_initial;
                let layoutAcross = layoutAcross_initial;

                if (tabId === 'innerText') {
                    // For inner text, these rules apply to how spreads are imposed.
                    if (applyEvenRuleToHeight) layoutDown = (layoutDown_initial > 0) ? floor(layoutDown_initial / 2) * 2 : 0;
                    if (applyEvenRuleToWidth) layoutAcross = (layoutAcross_initial > 0) ? floor(layoutAcross_initial / 2) * 2 : 0;
                }
                // For Cover/Endpapers, the item (itemDimH x itemDimW) is a single flat spread.
                // The even rules for "spreads across/down" don't apply in the same way when fitting these flat items N-up.
                // We just use the geometric fit for covers/endpapers.

                const geometricItemsPerSide = layoutDown * layoutAcross;
                if (geometricItemsPerSide <= 0) return null;

                let targetItemsPerSide = geometricItemsPerSide;
                if (tabId === 'innerText') {
                    targetItemsPerSide = Math.min(geometricItemsPerSide, 16); // Folding limit for inner text signatures
                }
                // No specific item cap (like 16) for covers or endpapers, they are usually 1-up or simple N-up.

                if (targetItemsPerSide <= 0) return null;

                let bestFittingItems = 0;
                let finalLayoutDown = 0;
                let finalLayoutAcross = 0;

                if (tabId === 'innerText') {
                    // Robust search for inner text to maximize page count within folding limits
                    for (let currentItems = targetItemsPerSide; currentItems >= 2; currentItems -= 2) { // Iterate by 2 for signatures
                        for (let rows = 1; rows <= currentItems; rows++) {
                            if (currentItems % rows === 0) {
                                let cols = currentItems / rows;
                                // Re-apply imposition rules if they were for spreads
                                if (applyEvenRuleToWidth && cols % 2 !== 0) continue;
                                if (applyEvenRuleToHeight && rows % 2 !== 0) continue;

                                const reqH = rows * itemDimH; const reqW = cols * itemDimW;
                                if (reqH <= usableH && reqW <= usableW) {
                                    if (currentItems > bestFittingItems) {
                                        bestFittingItems = currentItems;
                                        finalLayoutDown = rows; finalLayoutAcross = cols;
                                    }
                                    // TODO: Could add area efficiency tie-breaker here if currentItems === bestFittingItems
                                }
                            }
                        }
                        if (bestFittingItems > 0 && bestFittingItems === currentItems) break;
                    }
                     if (bestFittingItems === 0 && targetItemsPerSide === 1) { // Handle case where only 1 item might fit (e.g. single page)
                        if (itemDimH <= usableH && itemDimW <= usableW) {
                            bestFittingItems = 1; finalLayoutDown = 1; finalLayoutAcross = 1;
                        }
                    }
                } else { // For Cover & Endpapers - simpler: use the initial geometric N-up calculation
                    finalLayoutDown = layoutDown;       // From initial geometric calc based on itemDimH vs usableH
                    finalLayoutAcross = layoutAcross;   // From initial geometric calc based on itemDimW vs usableW
                    bestFittingItems = finalLayoutDown * finalLayoutAcross;
                }

                if (bestFittingItems <= 0) return null;

                const occupiedHeight = finalLayoutDown * itemDimH;
                const occupiedWidth = finalLayoutAcross * itemDimW;

                // Final check that the calculated layout actually fits
                if (occupiedHeight <= usableH && occupiedWidth <= usableW) {
                    return { layoutDown: finalLayoutDown, layoutAcross: finalLayoutAcross, fit: bestFittingItems, occupiedHeight, occupiedWidth };
                }
                return null; // Should not happen if logic above is correct
            }
            function selectWinningCandidate(candidateA, candidateB, requiredAlignmentMode) {
                const validCandidates = [candidateA, candidateB].filter(c => c !== null && c.maxItemsPerSide > 0);
                const matchingCandidates = validCandidates.filter(c => c.grainAlignmentStatus === requiredAlignmentMode);
                if (matchingCandidates.length === 0) return null;
                matchingCandidates.sort((x, y) => {
                    if (x.maxItemsPerSide !== y.maxItemsPerSide) return y.maxItemsPerSide - x.maxItemsPerSide;
                    const wasteX = (x.usableW * x.usableH - x.occupiedWidth * x.occupiedHeight);
                    const wasteY = (y.usableW * y.usableH - y.occupiedWidth * y.occupiedHeight);
                    return wasteX - wasteY;
                });
                return matchingCandidates[0];
            }
            function finalizeRollDimensions(candidate, gripper, colorBar, lip) {
                // Only trim if it's a roll AND one of its dimensions was maximized for the initial fitting.
                if (candidate.source !== 'Roll' || !candidate.rollDimensionOptimizedForFitting || candidate.error) {
                    return candidate;
                }

                // The rest of your trimming logic needs to be EXTREMELY CAREFUL
                // to identify which of candidate.pressH or candidate.pressW corresponds to the
                // dimension that was originally set using Math.max(maxPressH, maxPressW)
                // based on candidate.inputNoteKey ("resNoteHeightInferred" or "resNoteWidthInferred")
                // AND candidate.paperWasRotated.

                let finalPressH = parseFloat(candidate.pressH) || 0;
                let finalPressW = parseFloat(candidate.pressW) || 0;
                const occupiedH = parseFloat(candidate.occupiedHeight) || 0;
                const occupiedW = parseFloat(candidate.occupiedWidth) || 0;
                const totalVerticalMargins = gripper + colorBar;
                const totalHorizontalMargins = lip; // Ensure 'lip' is total lip

                if (occupiedH <= 0 || occupiedW <= 0) { // Should not happen if layout was valid
                    return candidate;
                }

                // Determine which press dimension was the one initially maximized (the cut-off length)
                if (candidate.inputNoteKey === "resNoteHeightInferred") {
                    // Original cut-off length was sheetH_for_initial_fit, which was maximized.
                    // This dimension is now either pressH (if not rotated) or pressW (if rotated).
                    if (candidate.paperWasRotated) { // Maximized initial H is now pressW
                        finalPressW = occupiedW + totalHorizontalMargins;
                    } else { // Maximized initial H is still pressH
                        finalPressH = occupiedH + totalVerticalMargins;
                    }
                } else if (candidate.inputNoteKey === "resNoteWidthInferred") {
                    // Original cut-off length was sheetW_for_initial_fit, which was maximized.
                    // This dimension is now either pressW (if not rotated) or pressH (if rotated).
                    if (candidate.paperWasRotated) { // Maximized initial W is now pressH
                        finalPressH = occupiedH + totalVerticalMargins;
                    } else { // Maximized initial W is still pressW
                        finalPressW = occupiedW + totalHorizontalMargins;
                    }
                } else {
                    // Should not reach here if rollDimensionOptimizedForFitting is true,
                    // as inputNoteKey should be set.
                    return candidate;
                }

                candidate.pressH = parseFloat(finalPressH.toFixed(1));
                candidate.pressW = parseFloat(finalPressW.toFixed(1));
                candidate.usableH = Math.max(0, parseFloat((candidate.pressH - totalVerticalMargins).toFixed(1)));
                candidate.usableW = Math.max(0, parseFloat((candidate.pressW - totalHorizontalMargins).toFixed(1)));
                candidate.rollOptimizationNoteKey = "rollDimensionsOptimized"; // Indicates trimming occurred

                return candidate;
            }

            function calculateFinalMetrics_ComponentSpecific(tabId, candidate, jobInputs, benchmarkValue) {
                try {
                    if (candidate.maxItemsPerSide <= 0) throw new Error("No items fit per sheet.");

                    // `candidate.maxItemsPerSide` is the number of "items" (pages for text, covers, endpaper spreads) per press sheet *side*.
                    // `pagesPerSheetOutput` should reflect items from *both sides* of the press sheet.

                    if (tabId === 'innerText') {
                        // For inner text, maxItemsPerSide is effectively pages per signature side.
                        // A signature (one item) produces maxItemsPerSide * 2 pages when folded.
                        candidate.pagesPerSheetOutput = candidate.maxItemsPerSide * 2;
                        if (candidate.pagesPerSheetOutput <= 0) throw new Error("Invalid pages per sheet output for Inner Text.");
                        candidate.totalSheetsNeeded = ceil(ceil(jobInputs.totalPages / candidate.pagesPerSheetOutput) * jobInputs.quantity * (1 + jobInputs.spoilagePct));
                    } else if (tabId === 'cover') {
                        // For covers, maxItemsPerSide is number of flat cover spreads N-up on one side of sheet.
                        // So, items from both sides of sheet = maxItemsPerSide * 2 (if printing both sides, usually not for covers unless ganged)
                        // Let's assume covers are printed 1-sided, so pagesPerSheetOutput is just maxItemsPerSide.
                        // If you gang multiple covers on a sheet that's printed work-and-turn/tumble, this logic might change.
                        // For simplicity, let's assume one cover item is produced per "item" counted in maxItemsPerSide.
                        const coversPerFullSheet = candidate.maxItemsPerSide; // Number of flat covers printed per sheet pass.
                        if (coversPerFullSheet <= 0) throw new Error("Invalid covers per sheet output.");
                        candidate.pagesPerSheetOutput = coversPerFullSheet; // For consistency in naming, though it's "covers"
                        candidate.totalSheetsNeeded = ceil(jobInputs.quantity / coversPerFullSheet * (1 + jobInputs.spoilagePct));
                    } else if (tabId === 'endpapers') {
                        // maxItemsPerSide is number of endpaper SPREADS that fit on one side of sheet.
                        // Each endpaper SPREAD typically forms 2 leaves (e.g., front pastedown + first flyleaf = 1 spread = 2 leaves = 4pp material).
                        const leavesPerEndpaperSpread = 2;
                        const totalLeavesFromOneSheetPass = candidate.maxItemsPerSide * leavesPerEndpaperSpread; // Leaves from one side of press sheet

                        candidate.pagesPerSheetOutput = totalLeavesFromOneSheetPass; // Using 'pages' to mean 'leaves' here for consistency.
                                                                                // If printing both sides of sheet for endpapers (rare but possible if ganging), this would be *2.
                                                                                // Assuming single-sided printing of endpaper stock.

                        const leavesNeededPerBook = jobInputs.endpaperType === 'double' ? 8 : 4;
                        if (candidate.pagesPerSheetOutput <= 0) throw new Error("Invalid endpaper leaves per sheet output.");

                        // How many books' worth of endpapers from one sheet pass
                        const booksWorthOfEndpapersPerSheet = candidate.pagesPerSheetOutput / leavesNeededPerBook;
                        if (booksWorthOfEndpapersPerSheet <= 0) throw new Error("Cannot get a full book's endpapers from a sheet.");

                        candidate.totalSheetsNeeded = ceil(jobInputs.quantity / booksWorthOfEndpapersPerSheet * (1 + jobInputs.spoilagePct));
                    }

                    // Cost per sheet (generic)
                    if (candidate.source === "Pre-Cut") {
                        if (candidate.costReam <= 0) throw new Error("Invalid Cost/Ream.");
                        candidate.costPerSheet = candidate.costReam / 500;
                    } else { // Roll
                        if (candidate.gsm <= 0 || candidate.costTonne < 0) throw new Error("Invalid GSM or Cost/Tonne for Roll.");
                        const areaM2 = (candidate.pressW / 1000) * (candidate.pressH / 1000);
                        if (areaM2 <= 0) throw new Error("Invalid press area for roll cost calculation.");
                        const weightKg = areaM2 * candidate.gsm / 1000;
                        candidate.costPerSheet = (weightKg * candidate.costTonne / 1000);
                    }
                    candidate.totalCost = candidate.totalSheetsNeeded * candidate.costPerSheet;

                    // Cost Per Book / Unit & Thickness
                    if (jobInputs.quantity > 0) {
                        candidate.costPerBook = candidate.totalCost / jobInputs.quantity;
                        if (tabId === 'innerText' && typeof candidate.caliperMicrons === 'number' && candidate.caliperMicrons > 0 && jobInputs.totalPages > 0) {
                            candidate.bookBlockThickness_mm = (jobInputs.totalPages / 2) * (candidate.caliperMicrons / 1000);
                        } else {
                            candidate.bookBlockThickness_mm = NaN;
                        }
                    } else { candidate.costPerBook = NaN; candidate.bookBlockThickness_mm = NaN; }

                    if (candidate.costPerSheet <= 0 || isNaN(candidate.costPerSheet) || candidate.totalCost === Infinity || isNaN(candidate.totalCost)) {
                         throw new Error("Invalid final cost calculation.");
                     }

                    const finalUsableArea = candidate.usableW * candidate.usableH;
                    const occupiedArea = candidate.occupiedWidth * candidate.occupiedHeight;
                    candidate.wastePercent = (finalUsableArea > 0 && occupiedArea <= finalUsableArea) ? Math.max(0, (finalUsableArea - occupiedArea)) / finalUsableArea : 1;
                    candidate.utilisationRate = 1.0 - candidate.wastePercent;
                    candidate.isOptimalCandidate = (candidate.maxItemsPerSide === benchmarkValue && !candidate.error);

                } catch (error) {
                    console.error(`Final Metrics error for ${tabId}, row ${candidate.id}:`, error.message, error.stack);
                    candidate.error = true;
                    candidate.errorMessageKey = candidate.costPerSheet <= 0 ? "errorCostSheetNegative" : (error.message.includes("Roll") ? "errorInvalidRollCost" : (error.message.includes("Pre-Cut") ? "errorMissingCostReam" : "errorCostCalc"));
                    candidate.totalCost = Infinity; candidate.costPerSheet = NaN; candidate.costPerBook = NaN; candidate.bookBlockThickness_mm = NaN;
                    candidate.wastePercent = 1.1; candidate.utilisationRate = -0.1; // Indicate error state
                }
            }


            tabButtons.forEach(button => {
                if(!button.dataset.tabListener) {
                    button.addEventListener('click', () => {
                        const targetTab = button.dataset.tab; currentTab = targetTab;
                        tabButtons.forEach(btn => btn.classList.remove('active')); button.classList.add('active');
                        tabContents.forEach(content => {
                            content.classList.remove('active');
                            if (content.id === `${targetTab}-tab-content`) {
                                content.classList.add('active');
                                content.querySelectorAll('.card-header.collapsible').forEach(header => {
                                    const cardContent = header.nextElementSibling;
                                    if (cardContent && !cardContent.classList.contains('user-collapsed')) {
                                        cardContent.classList.remove('collapsed');
                                        header.querySelector('.toggle-icon').classList.remove('collapsed');
                                        requestAnimationFrame(() => { // Allow DOM to render before getting scrollHeight
                                           cardContent.style.maxHeight = cardContent.scrollHeight + 'px';
                                        });
                                    }
                                });
                            }
                        });

                        // Only transfer values when switching to Cover or Endpapers tabs
                        if (targetTab === 'cover' || targetTab === 'endpapers') {
                            // Check if a paper option has been selected in the Inner Text tab
                            const hasPaperOptionSelected = selectedComponents.innerText.length > 0;

                            if (hasPaperOptionSelected) {
                                const innerH = safeParseFloat(document.getElementById('trim_h_innerText').value);
                                const innerW = safeParseFloat(document.getElementById('trim_w_innerText').value);
                                const innerQty = safeParseInt(document.getElementById('quantity_innerText').value);

                                // Set dimensions and quantity only if target fields are empty
                                if (targetTab === 'cover') {
                                    // Handle Cover tab fields
                                    const coverHEl = document.getElementById('cover_trim_h_cover');
                                    const coverWEl = document.getElementById('cover_trim_w_cover');
                                    const coverQtyEl = document.getElementById('quantity_cover');

                                    // Only set values if target fields are empty
                                    if (coverHEl && !coverHEl.value && innerH > 0) {
                                        coverHEl.value = innerH.toFixed(1);
                                        coverHEl.dispatchEvent(new Event('input', {bubbles:true}));
                                    }

                                    if (coverWEl && !coverWEl.value && innerW > 0) {
                                        coverWEl.value = innerW.toFixed(1);
                                        coverWEl.dispatchEvent(new Event('input', {bubbles:true}));
                                    }

                                    if (coverQtyEl && !coverQtyEl.value && innerQty > 0) {
                                        coverQtyEl.value = innerQty;
                                    }
                                } else if (targetTab === 'endpapers') {
                                    // Handle Endpapers tab fields
                                    const endpapersHEl = document.getElementById('trim_h_endpapers');
                                    const endpapersWEl = document.getElementById('trim_w_endpapers');
                                    const endpaperQtyEl = document.getElementById('quantity_endpapers');

                                    // Only set values if target fields are empty
                                    if (endpapersHEl && !endpapersHEl.value && innerH > 0) {
                                        endpapersHEl.value = innerH.toFixed(1);
                                        endpapersHEl.dispatchEvent(new Event('input', {bubbles:true}));
                                    }

                                    if (endpapersWEl && !endpapersWEl.value && innerW > 0) {
                                        endpapersWEl.value = innerW.toFixed(1);
                                        endpapersWEl.dispatchEvent(new Event('input', {bubbles:true}));
                                    }

                                    if (endpaperQtyEl && !endpaperQtyEl.value && innerQty > 0) {
                                        endpaperQtyEl.value = innerQty;
                                    }
                                }
                            }
                        }
                    });
                    button.dataset.tabListener = 'true';
                }
            });

            // Add event listener for binding method in Inner Text tab to auto-select corresponding cover type
            const bindingInnerText = document.getElementById('binding_innerText');
            if (bindingInnerText && !bindingInnerText.dataset.listenerAttached) {
                bindingInnerText.addEventListener('change', function() {
                    const coverTypeEl = document.getElementById('cover_type_cover');

                    // Only update cover type if:
                    // 1. A paper option has been selected in the Inner Text tab
                    // 2. AND the cover type element exists
                    if (coverTypeEl && selectedComponents.innerText.length > 0) {
                        // Map inner text binding methods to cover types
                        if (this.value === 'perfectBound') {
                            coverTypeEl.value = 'paperback';
                        } else if (this.value === 'caseBound') {
                            coverTypeEl.value = 'hardcover';
                        }
                        // Trigger the change event to update turn-in allowance visibility
                        coverTypeEl.dispatchEvent(new Event('change'));
                    }
                });
                bindingInnerText.dataset.listenerAttached = 'true';
            }

            // Function to toggle turn-in allowance and flap width visibility based on cover type
            function toggleCoverTypeSpecificFields() {
                const coverTypeSelect = document.getElementById('cover_type_cover');
                const turnInSection = document.getElementById('turn-in-allowance-section_cover');
                const flapWidthSection = document.getElementById('flap-width-section_cover');
                const coverTrimW = document.getElementById('cover_trim_w_cover');
                const flapWidth = document.getElementById('flap_width_cover');

                if (coverTypeSelect) {
                    const coverType = coverTypeSelect.value;

                    // Handle turn-in allowance visibility
                    if (turnInSection) {
                        if (coverType === 'hardcover') {
                            turnInSection.classList.remove('hidden');
                        } else {
                            turnInSection.classList.add('hidden');
                        }
                    }

                    // Handle flap width visibility and auto-calculation
                    if (flapWidthSection && flapWidth) {
                        if (coverType === 'dustJacket') {
                            flapWidthSection.classList.remove('hidden');

                            // Auto-calculate flap width as half the book width if not already set
                            if (!flapWidth.value && coverTrimW && coverTrimW.value) {
                                const bookWidth = safeParseFloat(coverTrimW.value);
                                if (bookWidth > 0) {
                                    flapWidth.value = (bookWidth / 2).toFixed(1);
                                }
                            }
                        } else {
                            flapWidthSection.classList.add('hidden');
                        }
                    }
                }
            }

            // Add event listener for cover type to toggle specific fields
            const coverTypeSelect = document.getElementById('cover_type_cover');
            if (coverTypeSelect && !coverTypeSelect.dataset.coverTypeListenerAttached) {
                coverTypeSelect.addEventListener('change', toggleCoverTypeSpecificFields);
                coverTypeSelect.dataset.coverTypeListenerAttached = 'true';
                // Initial check on page load
                toggleCoverTypeSpecificFields();
            }

            // Add event listener for cover width to update flap width when it changes
            const coverTrimW = document.getElementById('cover_trim_w_cover');
            if (coverTrimW && !coverTrimW.dataset.flapWidthListenerAttached) {
                coverTrimW.addEventListener('input', function() {
                    const coverTypeSelect = document.getElementById('cover_type_cover');
                    const flapWidth = document.getElementById('flap_width_cover');

                    if (coverTypeSelect && coverTypeSelect.value === 'dustJacket' && flapWidth && !flapWidth.value) {
                        const bookWidth = safeParseFloat(this.value);
                        if (bookWidth > 0) {
                            flapWidth.value = (bookWidth / 2).toFixed(1);
                        }
                    }
                });
                coverTrimW.dataset.flapWidthListenerAttached = 'true';
            }

            ['innerText', 'cover', 'endpapers'].forEach(tabId => {
                initializeSwitches(tabId);
                setupStandaloneConverter(tabId);

                // Use the correct IDs for each tab's unit converter fields
                if (tabId === 'cover') {
                    setupUnitConverterGroup('cover_trim_h_cover', 'cover_trim_h_in_cover');
                    setupUnitConverterGroup('cover_trim_w_cover', 'cover_trim_w_in_cover');
                    setupUnitConverterGroup('spine_thickness_cover', 'spine_thickness_in_cover');
                } else {
                    setupUnitConverterGroup(`trim_h_${tabId}`, `trim_h_in_${tabId}`);
                    setupUnitConverterGroup(`trim_w_${tabId}`, `trim_w_in_${tabId}`);
                }

                const addBtn = document.getElementById(`add-paper-option_${tabId}`);
                if(addBtn && !addBtn.dataset.listenerAttached){
                    addBtn.addEventListener('click',()=>addPaperOption(tabId));
                    addBtn.dataset.listenerAttached = 'true';
                }

                const calcBtn = document.getElementById(`calculate-btn_${tabId}`);
                if(calcBtn && !calcBtn.dataset.listenerAttached){
                    calcBtn.addEventListener('click',()=>calculateAndStoreResults(tabId));
                    calcBtn.dataset.listenerAttached = 'true';
                }

                const table = document.getElementById(`paper-options-table_${tabId}`);
                if(table && !table.dataset.listenerAttached){
                    // Improved event delegation for delete buttons to ensure clicks on SVG and path elements are captured
                    table.addEventListener('click', (e)=>{
                        // Find the closest .delete-row button from the event target
                        // This will work whether the user clicks the button, the SVG, or the path inside the SVG
                        const del = e.target.closest('.delete-row');
                        if(del){
                            const row = del.closest('tr');
                            if(row) deleteRow(row, tabId);
                        }
                    });
                    table.addEventListener('change', (e)=>{ if(e.target.classList.contains('source'))toggleRollPlaceholders(e.target.closest('tr'),tabId);});
                    table.addEventListener('input', (e)=>{ if(e.target.classList.contains('sheet-h')||e.target.classList.contains('sheet-w'))toggleRollPlaceholders(e.target.closest('tr'),tabId);});
                    table.dataset.listenerAttached = 'true';
                }
                // Apply placeholders to all existing rows
                document.querySelectorAll(`#paper-options-table_${tabId} tbody tr`).forEach(row => {
                    // Make sure all input fields have placeholders
                    const caliperInput = row.querySelector('.caliper-microns');
                    const costReamInput = row.querySelector('.cost-ream');
                    const gsmInput = row.querySelector('.gsm');
                    const costTonneInput = row.querySelector('.cost-tonne');

                    if (caliperInput && !caliperInput.placeholder) caliperInput.placeholder = "µm";
                    if (costReamInput && !costReamInput.placeholder) costReamInput.placeholder = "$";
                    if (gsmInput && !gsmInput.placeholder) gsmInput.placeholder = "g/m²";
                    if (costTonneInput && !costTonneInput.placeholder) costTonneInput.placeholder = "$";

                    // Apply roll-specific placeholders if needed
                    toggleRollPlaceholders(row, tabId);
                });
            });

            // Removed automatic quantity synchronization to make quantity fields independent
            // Values will only be transferred when switching tabs if target fields are empty


            // Add event listener for the close button
            const summaryCloseButton = document.getElementById('summary-close-button');
            if (summaryCloseButton) {
                summaryCloseButton.addEventListener('click', () => {
                    summaryPanel.classList.remove('visible');
                });
            }

            initializeCollapsibleCards();
            switchLanguage(currentLanguage);
            updateSummaryPanel();
            setupEnterKeyListeners(); // Initialize Enter key listeners
        });

        function setupEnterKeyListeners() {
            ['innerText', 'cover', 'endpapers'].forEach(tabId => {
                const tabContent = document.getElementById(`${tabId}-tab-content`);
                if (!tabContent) return;

                const calculateButton = document.getElementById(`calculate-btn_${tabId}`);
                if (!calculateButton) return;

                // Define specific input areas for Enter key listeners
                const JobSpecInputArea = tabContent.querySelector('.card:nth-child(1) .space-y-4'); // Job Specs card content
                const ProdParamInputArea = tabContent.querySelector('.card:nth-child(2) .grid.grid-cols-2.gap-4'); // Prod Params card content
                const PaperOptionsTable = document.getElementById(`paper-options-table_${tabId}`);

                const areasToListen = [JobSpecInputArea, ProdParamInputArea, PaperOptionsTable];

                areasToListen.forEach(area => {
                    if (area) {
                        area.addEventListener('keypress', function(event) {
                            if (event.key === 'Enter') {
                                const target = event.target;
                                // Check if the target is an input field within one of the designated areas
                                if (target.tagName === 'INPUT' && (target.type === 'number' || target.type === 'text')) {
                                    // Ensure it's not an input within the unit converter by checking parent structure
                                    // Unit converter inputs are within a div with class 'space-y-4 p-2' inside a card
                                    const unitConverterContent = target.closest('.card > .space-y-4.p-2');
                                    if (!unitConverterContent) {
                                        event.preventDefault(); // Prevent default form submission behavior
                                        calculateButton.click();
                                    }
                                }
                            }
                        });
                    }
                });
            });
        }
    </script>
</body>
</html>
